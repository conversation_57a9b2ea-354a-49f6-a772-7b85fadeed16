"use client"

import { useState } from "react"
import But<PERSON> from "@/modules/common/components/button"
import { DocumentText, CheckCircleSolid } from "@medusajs/icons"
import { Text, Heading } from "@medusajs/ui"

interface Order {
    id: string
    display_id: string
    created_at: string
    total: number
    currency_code: string
    payment_status: string
    fulfillment_status: string
}

interface RequestTaxInvoiceProps {
    orders: Order[]
    isLoading?: boolean
    onRequest?: (orderId: string) => Promise<void>
}

export default function RequestTaxInvoice({
    orders,
    isLoading,
    onRequest
}: RequestTaxInvoiceProps) {
    const [requestingOrders, setRequestingOrders] = useState<Set<string>>(new Set())

    const handleRequest = async (orderId: string) => {
        if (!onRequest) return

        setRequestingOrders(prev => new Set(prev).add(orderId))

        try {
            await onRequest(orderId)
        } catch (error) {
            console.error("Failed to request tax invoice:", error)
        } finally {
            setRequestingOrders(prev => {
                const newSet = new Set(prev)
                newSet.delete(orderId)
                return newSet
            })
        }
    }

    // Filter orders that are completed and paid
    const eligibleOrders = orders.filter(order =>
        order.payment_status === "captured" &&
        order.fulfillment_status !== "canceled"
    )

    if (isLoading) {
        return (
            <div className="space-y-4">
                <div className="text-center py-8">
                    <div className="animate-pulse">
                        <div className="h-6 bg-gray-200 rounded w-48 mx-auto mb-2"></div>
                        <div className="h-4 bg-gray-200 rounded w-64 mx-auto"></div>
                    </div>
                </div>
                {[...Array(3)].map((_, i) => (
                    <div key={i} className="border rounded-lg p-4 animate-pulse">
                        <div className="flex justify-between items-center">
                            <div className="space-y-2">
                                <div className="h-4 bg-gray-200 rounded w-32"></div>
                                <div className="h-3 bg-gray-200 rounded w-24"></div>
                            </div>
                            <div className="h-8 bg-gray-200 rounded w-20"></div>
                        </div>
                    </div>
                ))}
            </div>
        )
    }

    if (eligibleOrders.length === 0) {
        return (
            <div className="text-center py-12">
                <DocumentText className="mx-auto h-12 w-12 text-gray-400" />
                <Heading level="h3" className="mt-2 text-sm font-medium text-gray-900">
                    Tidak ada pesanan yang memenuhi syarat
                </Heading>
                <Text className="mt-1 text-sm text-gray-500">
                    Hanya pesanan yang sudah dibayar dan selesai yang dapat dimintakan faktur pajak.
                </Text>
            </div>
        )
    }

    return (
        <div className="space-y-6">
            <div className="text-center">
                <Heading level="h2" className="text-lg font-medium text-gray-900">
                    Minta Faktur Pajak
                </Heading>
                <Text className="mt-1 text-sm text-gray-500">
                    Pilih pesanan yang ingin dimintakan faktur pajak
                </Text>
            </div>

            <div className="space-y-4">
                {eligibleOrders.map((order) => (
                    <div key={order.id} className="border rounded-lg p-4 hover:shadow-sm transition-shadow">
                        <div className="flex justify-between items-center">
                            <div>
                                <div className="flex items-center gap-2">
                                    <Heading level="h3" className="text-sm font-medium text-gray-900">
                                        Pesanan #{order.display_id}
                                    </Heading>
                                    <span className="inline-flex items-center px-2 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800">
                                        <CheckCircleSolid className="w-3 h-3 mr-1" />
                                        Selesai
                                    </span>
                                </div>
                                <div className="mt-1 text-sm text-gray-500">
                                    <span>Tanggal: {new Date(order.created_at).toLocaleDateString("id-ID")}</span>
                                    <span className="mx-2">•</span>
                                    <span>
                                        Total: {new Intl.NumberFormat('id-ID', {
                                            style: 'currency',
                                            currency: order.currency_code.toUpperCase()
                                        }).format(order.total / 100)}
                                    </span>
                                </div>
                            </div>
                            <Button
                                variant="primary"
                                size="base"
                                onClick={() => handleRequest(order.id)}
                                disabled={requestingOrders.has(order.id)}
                                className="whitespace-nowrap"
                            >
                                {requestingOrders.has(order.id) ? (
                                    <span className="flex items-center gap-2">
                                        <span className="animate-spin">⏳</span>
                                        Meminta...
                                    </span>
                                ) : (
                                    "Minta Faktur Pajak"
                                )}
                            </Button>
                        </div>
                    </div>
                ))}
            </div>

            <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
                <div className="flex">
                    <div className="ml-3">
                        <Heading level="h3" className="text-sm font-medium text-blue-800">
                            Informasi Faktur Pajak
                        </Heading>
                        <div className="mt-2 text-sm text-blue-700">
                            <ul className="list-disc pl-5 space-y-1">
                                <li>Faktur pajak akan diproses dalam 1-3 hari kerja</li>
                                <li>Anda akan menerima notifikasi ketika faktur pajak siap diunduh</li>
                                <li>Faktur pajak akan tersedia dalam format PDF dan CSV e-Faktur</li>
                                <li>Pastikan data perusahaan dan NPWP sudah benar di profil Anda</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    )
}
