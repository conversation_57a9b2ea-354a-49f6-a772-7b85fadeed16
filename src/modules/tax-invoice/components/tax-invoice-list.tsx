"use client"

import { useState } from "react"
import Button from "@/modules/common/components/button"
import {
    DocumentText,
    CloudArrowDown,
    CheckCircleSolid,
    XCircleSolid,
    ExclamationCircleSolid
} from "@medusajs/icons"
import { convertToLocale } from "@/lib/util/money"

interface TaxInvoice {
    id: string
    order_id: string
    tax_invoice_number: string
    efaktur_number?: string
    status: "requested" | "processing" | "generated" | "sent" | "failed"
    taxable_amount?: number
    tax_amount?: number
    total_amount?: number
    tax_period?: string
    transaction_date?: Date
    requested_date: Date
    generated_date?: Date
    tax_invoice_pdf_url?: string
    efaktur_csv_url?: string
    rejection_reason?: string
    notes?: string
}

interface TaxInvoiceListProps {
    taxInvoices: TaxInvoice[]
    isLoading?: boolean
    onDownload?: (taxInvoice: TaxInvoice, type: 'pdf' | 'csv') => void
}

const getStatusIcon = (status: string) => {
    switch (status) {
        case "requested":
            return <span className="h-4 w-4 text-yellow-500 text-xs">⏳</span>
        case "processing":
            return <span className="h-4 w-4 text-blue-500 text-xs animate-pulse">🔄</span>
        case "generated":
        case "sent":
            return <CheckCircleSolid className="h-4 w-4 text-green-500" />
        case "failed":
            return <XCircleSolid className="h-4 w-4 text-red-500" />
        default:
            return <ExclamationCircleSolid className="h-4 w-4 text-gray-500" />
    }
}

const getStatusText = (status: string) => {
    switch (status) {
        case "requested":
            return "Diminta"
        case "processing":
            return "Diproses"
        case "generated":
            return "Dihasilkan"
        case "sent":
            return "Terkirim"
        case "failed":
            return "Gagal"
        default:
            return status
    }
}

const getStatusColor = (status: string) => {
    switch (status) {
        case "requested":
            return "bg-yellow-100 text-yellow-800"
        case "processing":
            return "bg-blue-100 text-blue-800"
        case "generated":
        case "sent":
            return "bg-green-100 text-green-800"
        case "failed":
            return "bg-red-100 text-red-800"
        default:
            return "bg-gray-100 text-gray-800"
    }
}

export default function TaxInvoiceList({
    taxInvoices,
    isLoading,
    onDownload
}: TaxInvoiceListProps) {
    if (isLoading) {
        return (
            <div className="space-y-4">
                {[...Array(3)].map((_, i) => (
                    <div key={i} className="border rounded-lg p-6 animate-pulse">
                        <div className="flex justify-between items-start mb-4">
                            <div className="space-y-2">
                                <div className="h-4 bg-gray-200 rounded w-48"></div>
                                <div className="h-3 bg-gray-200 rounded w-32"></div>
                            </div>
                            <div className="h-6 bg-gray-200 rounded w-20"></div>
                        </div>
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div className="h-3 bg-gray-200 rounded"></div>
                            <div className="h-3 bg-gray-200 rounded"></div>
                            <div className="h-3 bg-gray-200 rounded"></div>
                        </div>
                    </div>
                ))}
            </div>
        )
    }

    if (taxInvoices.length === 0) {
        return (
            <div className="text-center py-12">
                <DocumentText className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">
                    Tidak ada faktur pajak
                </h3>
                <p className="mt-1 text-sm text-gray-500">
                    Anda belum memiliki permintaan faktur pajak.
                </p>
            </div>
        )
    }

    return (
        <div className="space-y-4">
            {taxInvoices.map((taxInvoice) => (
                <div key={taxInvoice.id} className="border rounded-lg p-6 hover:shadow-md transition-shadow">
                    <div className="flex justify-between items-start mb-4">
                        <div>
                            <h3 className="text-lg font-medium text-gray-900">
                                {taxInvoice.tax_invoice_number}
                            </h3>
                            <p className="text-sm text-gray-500">
                                Pesanan: {taxInvoice.order_id}
                            </p>
                            {taxInvoice.efaktur_number && (
                                <p className="text-sm text-gray-500">
                                    e-Faktur: {taxInvoice.efaktur_number}
                                </p>
                            )}
                        </div>
                        <div className="flex items-center gap-2">
                            {getStatusIcon(taxInvoice.status)}
                            <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(taxInvoice.status)}`}>
                                {getStatusText(taxInvoice.status)}
                            </span>
                        </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                        <div>
                            <p className="text-sm font-medium text-gray-700">DPP</p>
                            <p className="text-sm text-gray-900">
                                {taxInvoice.taxable_amount
                                    ? convertToLocale({ amount: Number(taxInvoice.taxable_amount), currency_code: "IDR" })
                                    : '-'
                                }
                            </p>
                        </div>
                        <div>
                            <p className="text-sm font-medium text-gray-700">PPN</p>
                            <p className="text-sm text-gray-900">
                                {taxInvoice.tax_amount
                                    ? convertToLocale({ amount: Number(taxInvoice.tax_amount), currency_code: "IDR" })
                                    : '-'
                                }
                            </p>
                        </div>
                        <div>
                            <p className="text-sm font-medium text-gray-700">Total</p>
                            <p className="text-sm text-gray-900">
                                {taxInvoice.total_amount
                                    ? convertToLocale({ amount: Number(taxInvoice.total_amount), currency_code: "IDR" })
                                    : '-'
                                }
                            </p>
                        </div>
                    </div>

                    <div className="flex justify-between items-center text-sm text-gray-500 mb-4">
                        <span>
                            Diminta: {new Date(taxInvoice.requested_date).toLocaleDateString("id-ID")}
                        </span>
                        {taxInvoice.generated_date && (
                            <span>
                                Dihasilkan: {new Date(taxInvoice.generated_date).toLocaleDateString("id-ID")}
                            </span>
                        )}
                    </div>

                    {taxInvoice.rejection_reason && (
                        <div className="bg-red-50 border border-red-200 rounded-md p-3 mb-4">
                            <p className="text-sm text-red-800">
                                <strong>Alasan penolakan:</strong> {taxInvoice.rejection_reason}
                            </p>
                        </div>
                    )}

                    {taxInvoice.notes && (
                        <div className="bg-gray-50 border border-gray-200 rounded-md p-3 mb-4">
                            <p className="text-sm text-gray-700">
                                <strong>Catatan:</strong> {taxInvoice.notes}
                            </p>
                        </div>
                    )}

                    {(taxInvoice.status === "generated" || taxInvoice.status === "sent") && (
                        <div className="flex gap-2">
                            {taxInvoice.tax_invoice_pdf_url && onDownload && (
                                <Button
                                    variant="secondary"
                                    size="base"
                                    onClick={() => onDownload(taxInvoice, 'pdf')}
                                    className="flex items-center gap-2"
                                >
                                    <CloudArrowDown className="h-4 w-4" />
                                    Unduh PDF
                                </Button>
                            )}
                            {taxInvoice.efaktur_csv_url && onDownload && (
                                <Button
                                    variant="secondary"
                                    size="base"
                                    onClick={() => onDownload(taxInvoice, 'csv')}
                                    className="flex items-center gap-2"
                                >
                                    <CloudArrowDown className="h-4 w-4" />
                                    Unduh e-Faktur CSV
                                </Button>
                            )}
                        </div>
                    )}
                </div>
            ))}
        </div>
    )
}
