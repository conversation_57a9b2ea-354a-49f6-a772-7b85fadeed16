"use client"

import { useState, useEffect } from "react"
import { Tab } from "@headlessui/react"
import TaxInvoiceList from "./tax-invoice-list"
import RequestTaxInvoice from "./request-tax-invoice"
import { DocumentText, Plus } from "@medusajs/icons"
import { clx } from "@medusajs/ui"

interface TaxInvoice {
    id: string
    order_id: string
    tax_invoice_number: string
    efaktur_number?: string
    status: "requested" | "processing" | "generated" | "sent" | "failed"
    taxable_amount?: number
    tax_amount?: number
    total_amount?: number
    tax_period?: string
    transaction_date?: Date
    requested_date: Date
    generated_date?: Date
    tax_invoice_pdf_url?: string
    efaktur_csv_url?: string
    rejection_reason?: string
    notes?: string
}

interface Order {
    id: string
    display_id: string
    created_at: string
    total: number
    currency_code: string
    payment_status: string
    fulfillment_status: string
}

interface TaxInvoiceManagerProps {
    customerId?: string
}

export default function TaxInvoiceManager({ customerId }: TaxInvoiceManagerProps) {
    const [taxInvoices, setTaxInvoices] = useState<TaxInvoice[]>([])
    const [orders, setOrders] = useState<Order[]>([])
    const [isLoadingTaxInvoices, setIsLoadingTaxInvoices] = useState(true)
    const [isLoadingOrders, setIsLoadingOrders] = useState(true)

    // Fetch tax invoices
    const fetchTaxInvoices = async () => {
        if (!customerId) return

        try {
            setIsLoadingTaxInvoices(true)
            const response = await fetch('/store/tax-invoices', {
                headers: {
                    'Content-Type': 'application/json',
                },
                credentials: 'include'
            })

            if (response.ok) {
                const data = await response.json()
                setTaxInvoices(data.tax_invoices || [])
            }
        } catch (error) {
            console.error('Failed to fetch tax invoices:', error)
        } finally {
            setIsLoadingTaxInvoices(false)
        }
    }

    // Fetch orders
    const fetchOrders = async () => {
        if (!customerId) return

        try {
            setIsLoadingOrders(true)
            // This would typically fetch from the orders API
            // For now, we'll use a placeholder
            setOrders([])
        } catch (error) {
            console.error('Failed to fetch orders:', error)
        } finally {
            setIsLoadingOrders(false)
        }
    }

    // Request tax invoice for order
    const handleRequestTaxInvoice = async (orderId: string) => {
        try {
            const response = await fetch('/store/tax-invoices', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                credentials: 'include',
                body: JSON.stringify({
                    order_id: orderId
                })
            })

            if (response.ok) {
                const data = await response.json()
                // Refresh tax invoices list
                fetchTaxInvoices()

                // Show success message
                alert(data.message || 'Permintaan faktur pajak berhasil dibuat')
            } else {
                const error = await response.json()
                throw new Error(error.message || 'Gagal membuat permintaan faktur pajak')
            }
        } catch (error: any) {
            console.error('Failed to request tax invoice:', error)
            alert(error.message || 'Gagal membuat permintaan faktur pajak')
            throw error
        }
    }

    // Download tax invoice file
    const handleDownload = async (taxInvoice: TaxInvoice, type: 'pdf' | 'csv') => {
        try {
            const url = type === 'pdf' ? taxInvoice.tax_invoice_pdf_url : taxInvoice.efaktur_csv_url
            if (!url) return

            // In a real implementation, this would be a secure download endpoint
            window.open(url, '_blank')
        } catch (error) {
            console.error('Failed to download file:', error)
            alert('Gagal mengunduh file')
        }
    }

    useEffect(() => {
        fetchTaxInvoices()
        fetchOrders()
    }, [customerId])

    const tabs = [
        {
            name: 'Faktur Pajak Saya',
            icon: DocumentText,
            content: (
                <TaxInvoiceList
                    taxInvoices={taxInvoices}
                    isLoading={isLoadingTaxInvoices}
                    onDownload={handleDownload}
                />
            )
        },
        {
            name: 'Minta Faktur Pajak',
            icon: Plus,
            content: (
                <RequestTaxInvoice
                    orders={orders}
                    isLoading={isLoadingOrders}
                    onRequest={handleRequestTaxInvoice}
                />
            )
        }
    ]

    return (
        <div className="max-w-6xl mx-auto px-4 py-6">
            <div className="mb-8">
                <h1 className="text-2xl font-bold text-gray-900 mb-2">
                    Faktur Pajak
                </h1>
                <p className="text-gray-600">
                    Kelola permintaan dan unduh faktur pajak untuk pesanan Anda
                </p>
            </div>

            <Tab.Group>
                <Tab.List className="flex space-x-1 rounded-xl bg-gray-100 p-1 mb-6">
                    {tabs.map((tab, index) => (
                        <Tab
                            key={index}
                            className={({ selected }) =>
                                clx(
                                    'w-full rounded-lg py-2.5 text-sm font-medium leading-5 transition-all',
                                    'ring-white ring-opacity-60 ring-offset-2 ring-offset-blue-400 focus:outline-none focus:ring-2',
                                    selected
                                        ? 'bg-white text-blue-700 shadow'
                                        : 'text-gray-600 hover:bg-white/[0.12] hover:text-gray-800'
                                )
                            }
                        >
                            <div className="flex items-center justify-center gap-2">
                                <tab.icon className="h-5 w-5" />
                                {tab.name}
                            </div>
                        </Tab>
                    ))}
                </Tab.List>
                <Tab.Panels>
                    {tabs.map((tab, index) => (
                        <Tab.Panel
                            key={index}
                            className="rounded-xl bg-white p-3 ring-white ring-opacity-60 ring-offset-2 ring-offset-blue-400 focus:outline-none focus:ring-2"
                        >
                            {tab.content}
                        </Tab.Panel>
                    ))}
                </Tab.Panels>
            </Tab.Group>
        </div>
    )
}
