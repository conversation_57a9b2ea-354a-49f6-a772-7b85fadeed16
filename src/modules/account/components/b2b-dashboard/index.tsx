"use client"

import { Heading } from "@medusajs/ui"
import { 
  <PERSON>Cart, 
  <PERSON><PERSON>rencyDollar, 
  ClockSolid, 
  CheckCircleSolid,
  TruckSolid,
  DocumentText
} from "@medusajs/icons"
import Link from "next/link"

interface B2BDashboardProps {
  customer: {
    name: string
    company: string
    email: string
    memberSince: string
  }
  stats: {
    totalOrders: number
    totalSpent: number
    pendingOrders: number
    activeQuotes: number
  }
  recentOrders: Array<{
    id: string
    date: string
    total: number
    status: "pending" | "processing" | "shipped" | "delivered"
    items: number
  }>
  quickReorders: Array<{
    id: string
    name: string
    lastOrdered: string
    price: number
    image: string
  }>
}

const B2BDashboard = ({ customer, stats, recentOrders, quickReorders }: B2BDashboardProps) => {
  const statusColors = {
    pending: "bg-yellow-100 text-yellow-800",
    processing: "bg-blue-100 text-blue-800",
    shipped: "bg-purple-100 text-purple-800",
    delivered: "bg-green-100 text-green-800"
  }

  const status<PERSON>abels = {
    pending: "Menunggu",
    processing: "Diproses",
    shipped: "Dikirim",
    delivered: "Diterima"
  }

  return (
    <div className="max-w-7xl mx-auto px-4 py-8">
      {/* Welcome Header */}
      <div className="bg-gradient-to-r from-blue-600 to-indigo-600 rounded-xl p-6 text-white mb-8">
        <div className="flex items-center justify-between">
          <div>
            <Heading level="h1" className="text-2xl font-bold text-white mb-2">
              Selamat datang, {customer.name}
            </Heading>
            <p className="text-blue-100 mb-1">{customer.company}</p>
            <p className="text-blue-200 text-sm">Member sejak {customer.memberSince}</p>
          </div>
          <div className="text-right">
            <div className="bg-white/20 backdrop-blur-sm rounded-lg p-4">
              <div className="text-sm text-blue-100">Status Akun</div>
              <div className="text-lg font-semibold">Premium B2B</div>
            </div>
          </div>
        </div>
      </div>

      {/* Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div className="bg-white rounded-lg p-6 shadow-md border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Total Pesanan</p>
              <p className="text-2xl font-bold text-gray-900">{stats.totalOrders}</p>
            </div>
            <ShoppingCart className="w-8 h-8 text-blue-600" />
          </div>
        </div>

        <div className="bg-white rounded-lg p-6 shadow-md border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Total Belanja</p>
              <p className="text-2xl font-bold text-gray-900">
                Rp {stats.totalSpent.toLocaleString('id-ID')}
              </p>
            </div>
            <CurrencyDollar className="w-8 h-8 text-green-600" />
          </div>
        </div>

        <div className="bg-white rounded-lg p-6 shadow-md border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Pesanan Pending</p>
              <p className="text-2xl font-bold text-gray-900">{stats.pendingOrders}</p>
            </div>
            <ClockSolid className="w-8 h-8 text-yellow-600" />
          </div>
        </div>

        <div className="bg-white rounded-lg p-6 shadow-md border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Quote Aktif</p>
              <p className="text-2xl font-bold text-gray-900">{stats.activeQuotes}</p>
            </div>
            <DocumentText className="w-8 h-8 text-purple-600" />
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Recent Orders */}
        <div className="bg-white rounded-lg shadow-md border border-gray-200">
          <div className="p-6 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <h2 className="text-lg font-semibold text-gray-900">Pesanan Terbaru</h2>
              <Link href="/account/orders" className="text-blue-600 hover:text-blue-700 text-sm font-medium">
                Lihat Semua
              </Link>
            </div>
          </div>
          <div className="p-6">
            <div className="space-y-4">
              {recentOrders.map((order) => (
                <div key={order.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                  <div className="flex items-center space-x-4">
                    <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                      <ShoppingCart className="w-5 h-5 text-blue-600" />
                    </div>
                    <div>
                      <p className="font-medium text-gray-900">#{order.id}</p>
                      <p className="text-sm text-gray-600">{order.date} • {order.items} items</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="font-semibold text-gray-900">
                      Rp {order.total.toLocaleString('id-ID')}
                    </p>
                    <span className={`inline-block px-2 py-1 rounded-full text-xs font-medium ${statusColors[order.status]}`}>
                      {statusLabels[order.status]}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Quick Reorders */}
        <div className="bg-white rounded-lg shadow-md border border-gray-200">
          <div className="p-6 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <h2 className="text-lg font-semibold text-gray-900">Pesan Ulang Cepat</h2>
              <Link href="/account/order-history" className="text-blue-600 hover:text-blue-700 text-sm font-medium">
                Lihat Riwayat
              </Link>
            </div>
          </div>
          <div className="p-6">
            <div className="space-y-4">
              {quickReorders.map((item) => (
                <div key={item.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                  <div className="flex items-center space-x-4">
                    <div className="w-12 h-12 bg-gray-200 rounded-lg overflow-hidden">
                      <img 
                        src={item.image} 
                        alt={item.name}
                        className="w-full h-full object-cover"
                      />
                    </div>
                    <div>
                      <p className="font-medium text-gray-900 text-sm">{item.name}</p>
                      <p className="text-xs text-gray-600">Terakhir: {item.lastOrdered}</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="font-semibold text-gray-900 text-sm">
                      Rp {item.price.toLocaleString('id-ID')}
                    </p>
                    <button className="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-xs font-medium mt-1">
                      Pesan Lagi
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="mt-8 bg-white rounded-lg shadow-md border border-gray-200 p-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">Aksi Cepat</h2>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <Link href="/products" className="flex flex-col items-center p-4 bg-blue-50 rounded-lg hover:bg-blue-100 transition-colors">
            <ShoppingCart className="w-8 h-8 text-blue-600 mb-2" />
            <span className="text-sm font-medium text-blue-700">Belanja Produk</span>
          </Link>
          
          <Link href="/account/quotes" className="flex flex-col items-center p-4 bg-green-50 rounded-lg hover:bg-green-100 transition-colors">
            <DocumentText className="w-8 h-8 text-green-600 mb-2" />
            <span className="text-sm font-medium text-green-700">Minta Quote</span>
          </Link>
          
          <Link href="/account/orders" className="flex flex-col items-center p-4 bg-purple-50 rounded-lg hover:bg-purple-100 transition-colors">
            <TruckSolid className="w-8 h-8 text-purple-600 mb-2" />
            <span className="text-sm font-medium text-purple-700">Lacak Pesanan</span>
          </Link>
          
          <Link href="/contact" className="flex flex-col items-center p-4 bg-yellow-50 rounded-lg hover:bg-yellow-100 transition-colors">
            <CheckCircleSolid className="w-8 h-8 text-yellow-600 mb-2" />
            <span className="text-sm font-medium text-yellow-700">Hubungi Support</span>
          </Link>
        </div>
      </div>
    </div>
  )
}

export default B2BDashboard
