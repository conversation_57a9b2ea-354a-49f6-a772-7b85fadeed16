"use client"

import { clx } from "@medusajs/ui"
import LocalizedClientLink from "@/modules/common/components/localized-client-link"
import { usePathname } from "next/navigation"
import { useState } from "react"
import { ChevronDownMini } from "@medusajs/icons"

interface Subcategory {
  name: string
  href: string
}

interface IndustryCategory {
  name: string
  href: string
  description: string
  featured: boolean
  subcategories: Subcategory[]
}

interface SimpleCategory {
  name: string
  href: string
}

interface NavigationItem {
  name: string
  href: string
  children: (IndustryCategory | SimpleCategory)[]
}

const industryNavigation: NavigationItem[] = [
  {
    name: "<PERSON>duk",
    href: "#",
    children: [
      {
        name: "<PERSON><PERSON>",
        href: "/categories/medical-gloves",
        description: "Sarung tangan medis berkualitas tinggi",
        featured: true,
        subcategories: [
          { name: "Pemeriksaan", href: "/categories/examination-gloves" },
          { name: "<PERSON><PERSON>", href: "/categories/surgical-gloves" }
        ]
      },
      {
        name: "Industri",
        href: "/categories/industrial-gloves",
        description: "Perlindungan industri",
        featured: false,
        subcategories: [
          { name: "<PERSON>han <PERSON>", href: "/categories/cut-resistant-gloves" },
          { name: "Kimia", href: "/categories/chemical-gloves" }
        ]
      }
    ]
  }
]

const staticNavigation = [
  { name: "RFQ", href: "/quote-request" },
  { name: "Lacak Invoice", href: "/invoice-tracking" },
  { name: "Tentang", href: "/about" },
  { name: "Kontak", href: "/contact" }
]

const GloopiNavigation = () => {
  const [activeDropdown, setActiveDropdown] = useState<string | null>(null)
  const pathname = usePathname()

  const handleMouseEnter = (name: string) => {
    setActiveDropdown(name)
  }

  const handleMouseLeave = () => {
    setActiveDropdown(null)
  }

  return (
    <nav className="hidden lg:flex items-center space-x-8">
      {industryNavigation.map((item) => (
        <div
          key={item.name}
          className="relative"
          onMouseEnter={() => handleMouseEnter(item.name)}
          onMouseLeave={handleMouseLeave}
        >
          <button
            className={clx(
              "flex items-center space-x-1 text-sm font-medium transition-colors",
              activeDropdown === item.name
                ? "text-blue-600"
                : "text-gray-700 hover:text-blue-600"
            )}
          >
            <span>{item.name}</span>
            <ChevronDownMini className="w-4 h-4" />
          </button>

          {activeDropdown === item.name && (
            <div className="absolute top-full left-0 mt-2 w-screen max-w-4xl bg-white shadow-lg rounded-lg border border-gray-200 z-50">
              <div className="p-6">
                <div className="grid grid-cols-2 lg:grid-cols-4 gap-6">
                  {item.children.map((category) => {
                    const isIndustryCategory = 'description' in category
                    return (
                      <div key={category.name} className="space-y-3">
                        <LocalizedClientLink
                          href={category.href}
                          className="block group"
                        >
                          <div className="flex items-center space-x-2 mb-2">
                            <h3 className="font-semibold text-gray-900 group-hover:text-blue-600 transition-colors">
                              {category.name}
                            </h3>
                            {isIndustryCategory && (category as IndustryCategory).featured && (
                              <span className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">
                                Fase 1
                              </span>
                            )}
                          </div>
                          {isIndustryCategory && (
                            <p className="text-sm text-gray-600 group-hover:text-gray-800">
                              {(category as IndustryCategory).description}
                            </p>
                          )}
                        </LocalizedClientLink>

                        {isIndustryCategory && (category as IndustryCategory).subcategories && (
                          <ul className="space-y-2">
                            {(category as IndustryCategory).subcategories.map((sub: Subcategory) => (
                              <li key={sub.name}>
                                <LocalizedClientLink
                                  href={sub.href}
                                  className="text-sm text-gray-600 hover:text-blue-600 transition-colors"
                                >
                                  {sub.name}
                                </LocalizedClientLink>
                              </li>
                            ))}
                          </ul>
                        )}
                      </div>
                    )
                  })}
                </div>
              </div>
            </div>
          )}
        </div>
      ))}

      {staticNavigation.map((item) => (
        <LocalizedClientLink
          key={item.name}
          href={item.href}
          className={clx(
            "text-sm font-medium transition-colors",
            pathname === item.href
              ? "text-blue-600"
              : "text-gray-700 hover:text-blue-600"
          )}
        >
          {item.name}
        </LocalizedClientLink>
      ))}
    </nav>
  )
}

export default GloopiNavigation
