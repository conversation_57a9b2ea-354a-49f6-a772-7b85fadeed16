"use client";

import React, { useState, useEffect } from "react";
import { <PERSON><PERSON>, Container, Text, Heading, clx } from "@medusajs/ui";
import { CurrencyDollar, ShoppingCart, Clock } from "@medusajs/icons";

interface CompanyAnalyticsProps {
    companyId: string;
}

interface AnalyticsMetric {
    name: string;
    value: string | number;
    change: number;
    trend: "up" | "down" | "neutral";
    description: string;
}

export function CompanyAnalytics({ companyId }: CompanyAnalyticsProps) {
    const [loading, setLoading] = useState(true);
    const [selectedPeriod, setSelectedPeriod] = useState("month");

    // Mock data for demonstration
    const mockMetrics: AnalyticsMetric[] = [
        {
            name: "Total Pembelian",
            value: "Rp *************",
            change: 15.2,
            trend: "up",
            description: "Total nilai pembelian bulan ini"
        },
        {
            name: "<PERSON><PERSON><PERSON> Bulan Ini",
            value: "42",
            change: 8.1,
            trend: "up",
            description: "Jumlah pesanan yang dibuat"
        },
        {
            name: "Tingkat Approval",
            value: "96.4%",
            change: 2.3,
            trend: "up",
            description: "Persentase pesanan yang disetujui"
        },
        {
            name: "Waktu Rata-rata Approval",
            value: "2.4 hari",
            change: -12.5,
            trend: "up",
            description: "Waktu proses approval pesanan"
        }
    ];

    const recentOrders = [
        {
            id: "ORD-2024-001",
            date: "2024-11-15",
            amount: "Rp 45.000.000",
            status: "approved",
            items: 12
        },
        {
            id: "ORD-2024-002",
            date: "2024-11-12",
            amount: "Rp 32.000.000",
            status: "shipped",
            items: 8
        },
        {
            id: "ORD-2024-003",
            date: "2024-11-10",
            amount: "Rp 78.000.000",
            status: "delivered",
            items: 15
        }
    ];

    useEffect(() => {
        fetchAnalytics();
    }, [companyId, selectedPeriod]);

    const fetchAnalytics = async () => {
        try {
            setLoading(true);

            // Mock delay
            setTimeout(() => {
                setLoading(false);
            }, 1000);
        } catch (error) {
            console.error("Failed to fetch analytics:", error);
            setLoading(false);
        }
    };

    const getStatusColor = (status: string) => {
        switch (status) {
            case "approved": return "bg-green-100 text-green-800";
            case "shipped": return "bg-blue-100 text-blue-800";
            case "delivered": return "bg-purple-100 text-purple-800";
            case "pending": return "bg-yellow-100 text-yellow-800";
            default: return "bg-gray-100 text-gray-800";
        }
    };

    const getStatusText = (status: string) => {
        switch (status) {
            case "approved": return "Disetujui";
            case "shipped": return "Dikirim";
            case "delivered": return "Diterima";
            case "pending": return "Pending";
            default: return status;
        }
    };

    const getTrendEmoji = (trend: string) => {
        switch (trend) {
            case "up": return "📈";
            case "down": return "📉";
            default: return "➡️";
        }
    };

    if (loading) {
        return (
            <Container>
                <div className="flex items-center justify-center h-64">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                </div>
            </Container>
        );
    }

    return (
        <Container>
            <div className="space-y-8">
                {/* Header */}
                <div className="flex justify-between items-start">
                    <div>
                        <Heading level="h1" className="mb-2">Analytics Perusahaan</Heading>
                        <Text className="text-gray-600">
                            Monitor performa pembelian dan metrik penting perusahaan Anda
                        </Text>
                    </div>
                    <div className="flex space-x-2">
                        <Button variant="secondary" size="base">
                            📊 Export
                        </Button>
                    </div>
                </div>

                {/* Period Selector */}
                <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg w-fit">
                    {[
                        { value: "week", label: "Minggu Ini" },
                        { value: "month", label: "Bulan Ini" },
                        { value: "quarter", label: "Kuartal" },
                        { value: "year", label: "Tahun Ini" }
                    ].map((period) => (
                        <button
                            key={period.value}
                            onClick={() => setSelectedPeriod(period.value)}
                            className={clx(
                                "px-4 py-2 rounded-md text-sm font-medium transition-colors",
                                selectedPeriod === period.value
                                    ? "bg-white text-gray-900 shadow-sm"
                                    : "text-gray-600 hover:text-gray-900"
                            )}
                        >
                            {period.label}
                        </button>
                    ))}
                </div>

                {/* Key Metrics */}
                <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
                    {mockMetrics.map((metric, index) => (
                        <div key={index} className="border border-gray-200 rounded-lg p-6 bg-white">
                            <div className="flex items-center justify-between mb-3">
                                <Text size="small" weight="plus" className="text-gray-900">
                                    {metric.name}
                                </Text>
                                <span className="text-2xl">
                                    {index === 0 ? "💰" : index === 1 ? "🛒" : index === 2 ? "✅" : "⏱️"}
                                </span>
                            </div>
                            <div className="mb-2">
                                <Text size="xlarge" weight="plus" className="text-gray-900">
                                    {metric.value}
                                </Text>
                            </div>
                            <div className="flex items-center text-xs mb-2">
                                <span className="mr-1">{getTrendEmoji(metric.trend)}</span>
                                <Text
                                    size="xsmall"
                                    className={metric.trend === "up" ? "text-green-600" : metric.trend === "down" ? "text-red-600" : "text-gray-500"}
                                >
                                    {metric.change > 0 ? "+" : ""}{metric.change}% dari bulan lalu
                                </Text>
                            </div>
                            <Text size="xsmall" className="text-gray-500">
                                {metric.description}
                            </Text>
                        </div>
                    ))}
                </div>

                {/* Charts Placeholder */}
                <div className="grid gap-6 md:grid-cols-2">
                    <div className="border border-gray-200 rounded-lg p-6 bg-white">
                        <div className="mb-4">
                            <Heading level="h3" className="mb-1">Status Pesanan</Heading>
                            <Text size="small" className="text-gray-600">
                                Distribusi status pesanan bulan ini
                            </Text>
                        </div>
                        <div className="space-y-3">
                            {[
                                { status: "Disetujui", count: 38, color: "bg-green-500" },
                                { status: "Pending", count: 3, color: "bg-yellow-500" },
                                { status: "Ditolak", count: 1, color: "bg-red-500" },
                            ].map((item, index) => (
                                <div key={index} className="flex items-center justify-between">
                                    <div className="flex items-center space-x-3">
                                        <div className={`w-3 h-3 rounded-full ${item.color}`}></div>
                                        <Text size="small" weight="plus">{item.status}</Text>
                                    </div>
                                    <div className="flex items-center space-x-2">
                                        <Text size="small" weight="plus">{item.count}</Text>
                                        <Text size="xsmall" className="text-gray-500">
                                            ({((item.count / 42) * 100).toFixed(1)}%)
                                        </Text>
                                    </div>
                                </div>
                            ))}
                        </div>
                    </div>

                    <div className="border border-gray-200 rounded-lg p-6 bg-white">
                        <div className="mb-4">
                            <Heading level="h3" className="mb-1">Statistik Cepat</Heading>
                            <Text size="small" className="text-gray-600">
                                Informasi penting dalam sekilas
                            </Text>
                        </div>
                        <div className="space-y-4">
                            <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                                <div>
                                    <Text size="small" className="text-gray-600">Rata-rata Order Value</Text>
                                    <Text size="base" weight="plus">Rp 28.750.000</Text>
                                </div>
                                <span className="text-green-500 text-xl">📈</span>
                            </div>
                            <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                                <div>
                                    <Text size="small" className="text-gray-600">Frekuensi Pemesanan</Text>
                                    <Text size="base" weight="plus">2.1 per minggu</Text>
                                </div>
                                <span className="text-blue-500 text-xl">📅</span>
                            </div>
                            <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                                <div>
                                    <Text size="small" className="text-gray-600">Top Category</Text>
                                    <Text size="base" weight="plus">Electronics</Text>
                                </div>
                                <span className="text-purple-500 text-xl">🛒</span>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Recent Orders */}
                <div className="border border-gray-200 rounded-lg p-6 bg-white">
                    <div className="mb-6">
                        <Heading level="h3" className="mb-1">Pesanan Terbaru</Heading>
                        <Text size="small" className="text-gray-600">
                            Pesanan terbaru dari perusahaan Anda
                        </Text>
                    </div>
                    <div className="space-y-3">
                        {recentOrders.map((order, index) => (
                            <div key={index} className="flex items-center justify-between p-4 border border-gray-100 rounded-lg hover:bg-gray-50 transition-colors">
                                <div className="flex items-center space-x-4">
                                    <div>
                                        <Text weight="plus" className="text-gray-900">{order.id}</Text>
                                        <Text size="small" className="text-gray-500">{order.date}</Text>
                                    </div>
                                    <div>
                                        <Text size="small" className="text-gray-600">{order.items} items</Text>
                                    </div>
                                </div>
                                <div className="flex items-center space-x-4">
                                    <div className="text-right">
                                        <Text weight="plus" className="text-gray-900">{order.amount}</Text>
                                    </div>
                                    <span className={clx(
                                        "px-2 py-1 rounded-md text-xs font-medium",
                                        getStatusColor(order.status)
                                    )}>
                                        {getStatusText(order.status)}
                                    </span>
                                </div>
                            </div>
                        ))}
                    </div>
                </div>
            </div>
        </Container>
    );
}
