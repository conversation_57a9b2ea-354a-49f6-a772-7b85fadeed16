"use client"

import { useState } from "react"
import { Button, Input, Textarea } from "@medusajs/ui"
import { CloudArrowUp, CheckCircleSolid, DocumentText } from "@medusajs/icons"

interface PaymentProofData {
    amount: string
    transfer_date: string
    bank_from: string
    bank_to: string
    reference_number: string
    notes: string
    proof_file: File | null
}

interface PaymentProofUploadProps {
    invoiceId: string
    invoiceNumber: string
    dpAmount: number
    onUploadSuccess?: () => void
}

const PaymentProofUpload = ({
    invoiceId,
    invoiceNumber,
    dpAmount,
    onUploadSuccess
}: PaymentProofUploadProps) => {
    const [formData, setFormData] = useState<PaymentProofData>({
        amount: dpAmount.toString(),
        transfer_date: "",
        bank_from: "",
        bank_to: "",
        reference_number: "",
        notes: "",
        proof_file: null
    })

    const [isUploading, setIsUploading] = useState(false)
    const [isSuccess, setIsSuccess] = useState(false)
    const [error, setError] = useState("")

    const handleInputChange = (field: keyof PaymentProofData, value: string) => {
        setFormData(prev => ({
            ...prev,
            [field]: value
        }))
    }

    const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const file = e.target.files?.[0] || null
        setFormData(prev => ({
            ...prev,
            proof_file: file
        }))
    }

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault()
        setIsUploading(true)
        setError("")

        try {
            const formDataToSend = new FormData()

            // Add all form fields
            Object.entries(formData).forEach(([key, value]) => {
                if (key === "proof_file" && value instanceof File) {
                    formDataToSend.append(key, value)
                } else if (typeof value === "string") {
                    formDataToSend.append(key, value)
                }
            })

            const response = await fetch(`/api/store/invoices/${invoiceId}/payment-proof`, {
                method: "POST",
                body: formDataToSend
            })

            if (!response.ok) {
                throw new Error("Gagal mengupload bukti pembayaran")
            }

            setIsSuccess(true)
            onUploadSuccess?.()
        } catch (err) {
            setError(err instanceof Error ? err.message : "Terjadi kesalahan")
        } finally {
            setIsUploading(false)
        }
    }

    const formatCurrency = (amount: number) => {
        return new Intl.NumberFormat("id-ID", {
            style: "currency",
            currency: "IDR",
            minimumFractionDigits: 0,
        }).format(amount)
    }

    if (isSuccess) {
        return (
            <div className="max-w-lg mx-auto text-center p-8">
                <div className="mx-auto h-16 w-16 rounded-full bg-green-100 flex items-center justify-center mb-6">
                    <CheckCircleSolid className="h-8 w-8 text-green-600" />
                </div>
                <h3 className="text-2xl font-bold text-gray-900 mb-4">
                    Bukti Pembayaran Berhasil Diupload!
                </h3>
                <p className="text-lg text-gray-600 mb-6">
                    Tim kami akan memverifikasi pembayaran Anda dalam waktu 1 hari kerja.
                    Anda akan mendapat notifikasi setelah verifikasi selesai.
                </p>
                <div className="bg-blue-50 rounded-lg p-4 text-left">
                    <h4 className="font-semibold text-blue-900 mb-2">Langkah Selanjutnya:</h4>
                    <ul className="space-y-1 text-blue-800">
                        <li>• Tunggu konfirmasi verifikasi pembayaran</li>
                        <li>• Proses produksi akan dimulai setelah DP dikonfirmasi</li>
                        <li>• Anda akan mendapat update status produksi</li>
                        <li>• Pelunasan dilakukan sebelum pengiriman</li>
                    </ul>
                </div>
            </div>
        )
    }

    return (
        <div className="max-w-2xl mx-auto">
            <div className="text-center mb-8">
                <CloudArrowUp className="mx-auto h-12 w-12 text-blue-600 mb-4" />
                <h2 className="text-3xl font-bold text-gray-900">
                    Upload Bukti Pembayaran
                </h2>
                <p className="mt-4 text-lg text-gray-600">
                    Invoice: <span className="font-semibold">{invoiceNumber}</span>
                </p>
                <p className="text-sm text-gray-500">
                    Jumlah DP: {formatCurrency(dpAmount)}
                </p>
            </div>

            <form onSubmit={handleSubmit} className="space-y-6">
                {/* Payment Information */}
                <div className="bg-gray-50 rounded-lg p-6">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">Informasi Transfer</h3>
                    <div className="grid gap-4 md:grid-cols-2">
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                Jumlah Transfer *
                            </label>
                            <Input
                                required
                                value={formData.amount}
                                onChange={(e) => handleInputChange("amount", e.target.value)}
                                placeholder="Contoh: 5000000"
                            />
                        </div>
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                Tanggal Transfer *
                            </label>
                            <Input
                                type="date"
                                required
                                value={formData.transfer_date}
                                onChange={(e) => handleInputChange("transfer_date", e.target.value)}
                            />
                        </div>
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                Bank Pengirim *
                            </label>
                            <Input
                                required
                                value={formData.bank_from}
                                onChange={(e) => handleInputChange("bank_from", e.target.value)}
                                placeholder="Contoh: BCA"
                            />
                        </div>
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                Bank Tujuan *
                            </label>
                            <Input
                                required
                                value={formData.bank_to}
                                onChange={(e) => handleInputChange("bank_to", e.target.value)}
                                placeholder="Contoh: BCA"
                            />
                        </div>
                        <div className="md:col-span-2">
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                No. Referensi/Transaksi
                            </label>
                            <Input
                                value={formData.reference_number}
                                onChange={(e) => handleInputChange("reference_number", e.target.value)}
                                placeholder="Nomor referensi dari bank (opsional)"
                            />
                        </div>
                    </div>
                </div>

                {/* File Upload */}
                <div className="bg-gray-50 rounded-lg p-6">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">Upload Bukti Transfer</h3>
                    <div className="space-y-4">
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                File Bukti Transfer * (JPG, PNG, PDF - Max 5MB)
                            </label>
                            <input
                                type="file"
                                accept="image/jpeg,image/png,application/pdf"
                                onChange={handleFileChange}
                                required
                                className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
                            />
                            {formData.proof_file && (
                                <p className="mt-2 text-sm text-green-600">
                                    File terpilih: {formData.proof_file.name}
                                </p>
                            )}
                        </div>

                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                Catatan Tambahan
                            </label>
                            <Textarea
                                value={formData.notes}
                                onChange={(e) => handleInputChange("notes", e.target.value)}
                                placeholder="Informasi tambahan tentang transfer ini..."
                                rows={3}
                            />
                        </div>
                    </div>
                </div>

                {/* Error Message */}
                {error && (
                    <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                        <p className="text-red-800">{error}</p>
                    </div>
                )}

                {/* Submit Button */}
                <Button
                    type="submit"
                    size="large"
                    className="w-full"
                    disabled={isUploading}
                >
                    {isUploading ? "Mengupload..." : "Upload Bukti Pembayaran"}
                </Button>

                {/* Info */}
                <div className="bg-blue-50 rounded-lg p-4">
                    <h4 className="font-semibold text-blue-900 mb-2">Informasi Penting:</h4>
                    <ul className="space-y-1 text-blue-800 text-sm">
                        <li>• Pastikan jumlah transfer sesuai dengan invoice</li>
                        <li>• File harus jelas dan dapat dibaca</li>
                        <li>• Verifikasi akan dilakukan dalam 1 hari kerja</li>
                        <li>• Hubungi customer service jika ada masalah</li>
                    </ul>
                </div>
            </form>
        </div>
    )
}

export default PaymentProofUpload
