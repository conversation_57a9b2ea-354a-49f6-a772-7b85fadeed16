"use client"

import { useState } from "react"
import { Button, Input } from "@medusajs/ui"
import { MagnifyingGlassMini, DocumentText, CheckCircleSolid } from "@medusajs/icons"

interface InvoiceData {
    id: string
    invoice_number: string
    company_name: string
    total_amount: number
    dp_amount: number
    remaining_amount: number
    status: string
    due_date: string
    order_id: string
    payment_proofs: any[]
}

const InvoiceTracker = () => {
    const [invoiceNumber, setInvoiceNumber] = useState("")
    const [isSearching, setIsSearching] = useState(false)
    const [invoice, setInvoice] = useState<InvoiceData | null>(null)
    const [error, setError] = useState("")

    const handleSearch = async (e: React.FormEvent) => {
        e.preventDefault()
        if (!invoiceNumber.trim()) return

        setIsSearching(true)
        setError("")

        try {
            const response = await fetch(`/api/store/invoices/track?number=${invoiceNumber}`)

            if (!response.ok) {
                throw new Error("Invoice tidak ditemukan")
            }

            const data = await response.json()
            setInvoice(data.invoice)
        } catch (err) {
            setError(err instanceof Error ? err.message : "Terjadi kesalahan")
            setInvoice(null)
        } finally {
            setIsSearching(false)
        }
    }

    const formatCurrency = (amount: number) => {
        return new Intl.NumberFormat("id-ID", {
            style: "currency",
            currency: "IDR",
            minimumFractionDigits: 0,
        }).format(amount)
    }

    const getStatusColor = (status: string) => {
        switch (status) {
            case "paid": return "bg-green-100 text-green-800"
            case "partial": return "bg-yellow-100 text-yellow-800"
            case "pending": return "bg-gray-100 text-gray-800"
            case "overdue": return "bg-red-100 text-red-800"
            default: return "bg-gray-100 text-gray-800"
        }
    }

    const getStatusText = (status: string) => {
        switch (status) {
            case "paid": return "Lunas"
            case "partial": return "Bayar Sebagian"
            case "pending": return "Menunggu Pembayaran"
            case "overdue": return "Terlambat"
            default: return status
        }
    }

    return (
        <div className="max-w-4xl mx-auto p-6">
            <div className="text-center mb-8">
                <DocumentText className="mx-auto h-12 w-12 text-blue-600 mb-4" />
                <h1 className="text-3xl font-bold text-gray-900 mb-2">
                    Lacak Status Invoice
                </h1>
                <p className="text-lg text-gray-600">
                    Masukkan nomor invoice untuk melihat status pembayaran dan detail
                </p>
            </div>

            {/* Search Form */}
            <form onSubmit={handleSearch} className="mb-8">
                <div className="flex gap-4 max-w-md mx-auto">
                    <Input
                        value={invoiceNumber}
                        onChange={(e) => setInvoiceNumber(e.target.value)}
                        placeholder="Contoh: INV-001"
                        className="flex-1"
                    />
                    <Button type="submit" disabled={isSearching}>
                        <MagnifyingGlassMini className="mr-2 h-4 w-4" />
                        {isSearching ? "Mencari..." : "Cari"}
                    </Button>
                </div>
            </form>

            {/* Error Message */}
            {error && (
                <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6 text-center">
                    <p className="text-red-800">{error}</p>
                </div>
            )}

            {/* Invoice Details */}
            {invoice && (
                <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
                    {/* Header */}
                    <div className="bg-gradient-to-r from-blue-600 to-teal-600 px-6 py-4">
                        <div className="flex items-center justify-between">
                            <div>
                                <h2 className="text-xl font-bold text-white">
                                    {invoice.invoice_number}
                                </h2>
                                <p className="text-blue-100">
                                    {invoice.company_name}
                                </p>
                            </div>
                            <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(invoice.status)}`}>
                                {getStatusText(invoice.status)}
                            </span>
                        </div>
                    </div>

                    {/* Content */}
                    <div className="p-6">
                        <div className="grid gap-6 lg:grid-cols-2">
                            {/* Invoice Info */}
                            <div>
                                <h3 className="text-lg font-semibold text-gray-900 mb-4">
                                    Informasi Invoice
                                </h3>
                                <div className="space-y-3">
                                    <div className="flex justify-between">
                                        <span className="text-gray-600">Total Invoice:</span>
                                        <span className="font-semibold">{formatCurrency(invoice.total_amount)}</span>
                                    </div>
                                    <div className="flex justify-between">
                                        <span className="text-gray-600">DP yang Harus Dibayar:</span>
                                        <span className="font-semibold text-blue-600">{formatCurrency(invoice.dp_amount)}</span>
                                    </div>
                                    <div className="flex justify-between">
                                        <span className="text-gray-600">Sisa Pembayaran:</span>
                                        <span className="font-semibold">{formatCurrency(invoice.remaining_amount)}</span>
                                    </div>
                                    <div className="flex justify-between">
                                        <span className="text-gray-600">Jatuh Tempo:</span>
                                        <span className="font-semibold">{new Date(invoice.due_date).toLocaleDateString("id-ID")}</span>
                                    </div>
                                    <div className="flex justify-between">
                                        <span className="text-gray-600">No. Order:</span>
                                        <span className="font-semibold">{invoice.order_id}</span>
                                    </div>
                                </div>
                            </div>

                            {/* Payment Status */}
                            <div>
                                <h3 className="text-lg font-semibold text-gray-900 mb-4">
                                    Status Pembayaran
                                </h3>

                                {invoice.payment_proofs.length > 0 ? (
                                    <div className="space-y-3">
                                        {invoice.payment_proofs.map((proof: any, index: number) => (
                                            <div key={index} className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                                                <div>
                                                    <p className="font-medium text-green-800">
                                                        Bukti Pembayaran #{index + 1}
                                                    </p>
                                                    <p className="text-sm text-green-600">
                                                        {formatCurrency(proof.amount)} - {new Date(proof.created_at).toLocaleDateString("id-ID")}
                                                    </p>
                                                </div>
                                                <CheckCircleSolid className="h-5 w-5 text-green-500" />
                                            </div>
                                        ))}
                                    </div>
                                ) : (
                                    <div className="text-center p-6 bg-gray-50 rounded-lg">
                                        <p className="text-gray-600 mb-4">
                                            Belum ada bukti pembayaran yang diupload
                                        </p>
                                        <Button variant="secondary">
                                            Upload Bukti Pembayaran
                                        </Button>
                                    </div>
                                )}
                            </div>
                        </div>

                        {/* Payment Instructions */}
                        {invoice.status !== "paid" && (
                            <div className="mt-8 p-6 bg-blue-50 rounded-lg">
                                <h4 className="font-semibold text-blue-900 mb-3">
                                    Instruksi Pembayaran
                                </h4>
                                <div className="space-y-2 text-blue-800">
                                    <p>• Transfer ke rekening yang tertera dalam invoice</p>
                                    <p>• Upload bukti transfer melalui akun Anda</p>
                                    <p>• Konfirmasi pembayaran akan diproses dalam 1 hari kerja</p>
                                    <p>• Hubungi customer service jika ada kendala</p>
                                </div>
                            </div>
                        )}
                    </div>
                </div>
            )}

            {/* Help Section */}
            <div className="mt-12 text-center">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">
                    Butuh Bantuan?
                </h3>
                <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-3">
                    <div className="p-4 bg-white rounded-lg border border-gray-200">
                        <h4 className="font-medium text-gray-900 mb-2">WhatsApp</h4>
                        <p className="text-sm text-gray-600 mb-3">
                            Chat langsung dengan tim customer service
                        </p>
                        <Button variant="secondary" size="small">
                            Chat Sekarang
                        </Button>
                    </div>

                    <div className="p-4 bg-white rounded-lg border border-gray-200">
                        <h4 className="font-medium text-gray-900 mb-2">Email</h4>
                        <p className="text-sm text-gray-600 mb-3">
                            Kirim email untuk pertanyaan detail
                        </p>
                        <Button variant="secondary" size="small">
                            Kirim Email
                        </Button>
                    </div>

                    <div className="p-4 bg-white rounded-lg border border-gray-200">
                        <h4 className="font-medium text-gray-900 mb-2">Telepon</h4>
                        <p className="text-sm text-gray-600 mb-3">
                            Hubungi langsung via telepon
                        </p>
                        <Button variant="secondary" size="small">
                            Telepon
                        </Button>
                    </div>
                </div>
            </div>
        </div>
    )
}

export default InvoiceTracker
