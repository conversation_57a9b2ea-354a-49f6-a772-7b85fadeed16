"use client"

import { Heading } from "@medusajs/ui"
import Button from "@/modules/common/components/button"
import Image from "next/image"
import Link from "next/link"

const Hero = () => {
  return (
    <div className="h-[75vh] w-full border-b border-ui-border-base relative bg-gradient-to-r from-blue-50 to-green-50">
      <div className="absolute inset-0 bg-black/20" />
      <Image
        src="https://images.unsplash.com/photo-1584308666744-24d5c474f2ae?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80"
        alt="Sarung tangan berkualitas tinggi untuk berbagai industri"
        fill
        className="object-cover"
        quality={100}
        priority
      />
      <div className="absolute inset-0 z-10 flex flex-col justify-center items-center text-center px-4 small:px-32 gap-6">
        <div className="max-w-4xl">
          <p className="text-white text-sm uppercase tracking-wide mb-4">
            Solusi Sarung Tangan Terpercaya
          </p>

          <Heading
            level="h1"
            className="text-4xl small:text-6xl leading-tight text-white font-bold mb-6"
          >
            Sarung Tangan Berkualitas untuk Semua Industri
          </Heading>

          <p className="text-white/90 font-normal text-lg small:text-xl mb-8 max-w-2xl mx-auto">
            Dari medis hingga industri, kami menyediakan sarung tangan dengan standar internasional untuk kebutuhan B2B dan B2C Anda
          </p>
        </div>
        
        <div className="flex flex-col small:flex-row gap-4">
          <Link href="/id/categories/medical-gloves">
            <Button className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 rounded-lg font-semibold">
              Belanja Sarung Tangan Medis
            </Button>
          </Link>
          <Link href="/id/store">
            <Button variant="secondary" className="bg-white/90 hover:bg-white text-gray-800 px-8 py-3 rounded-lg font-semibold">
              Lihat Semua Produk
            </Button>
          </Link>
        </div>
      </div>
    </div>
  )
}

export default Hero
