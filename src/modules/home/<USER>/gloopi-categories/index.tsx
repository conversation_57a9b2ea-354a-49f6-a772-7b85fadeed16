import { Button } from "@medusajs/ui"
import LocalizedClientLink from "@/modules/common/components/localized-client-link"
import Image from "next/image"

interface CategoryCardProps {
    title: string
    description: string
    imageUrl: string
    slug: string
    features: string[]
}

const CategoryCard = ({ title, description, imageUrl, slug, features }: CategoryCardProps) => {
    return (
        <div className="group relative overflow-hidden rounded-2xl bg-white shadow-sm border border-gray-100 hover:shadow-lg transition-all duration-300">
            {/* Image Container */}
            <div className="aspect-[4/3] overflow-hidden bg-gray-100">
                <div className="h-full w-full bg-gradient-to-br from-blue-50 to-teal-50 flex items-center justify-center">
                    <div className="text-center p-8">
                        <div className="mx-auto h-20 w-20 rounded-full bg-blue-100 flex items-center justify-center mb-4">
                            <span className="text-2xl">🧤</span>
                        </div>
                        <p className="text-sm text-gray-600 font-medium">{title}</p>
                    </div>
                </div>
            </div>

            {/* Content */}
            <div className="p-6">
                <h3 className="text-xl font-semibold text-gray-900 mb-2">{title}</h3>
                <p className="text-gray-600 mb-4">{description}</p>

                {/* Features */}
                <ul className="space-y-1 mb-6">
                    {features.map((feature, index) => (
                        <li key={index} className="text-sm text-gray-500 flex items-center">
                            <span className="w-1.5 h-1.5 bg-blue-400 rounded-full mr-2 flex-shrink-0"></span>
                            {feature}
                        </li>
                    ))}
                </ul>

                <LocalizedClientLink href={`/categories/${slug}`}>
                    <Button
                        variant="secondary"
                        className="w-full group-hover:bg-blue-600 group-hover:text-white transition-colors"
                    >
                        Lihat Produk
                    </Button>
                </LocalizedClientLink>
            </div>
        </div>
    )
}

const GloopiCategories = () => {
    const categories = [
        {
            title: "Sarung Tangan Medis",
            description: "Untuk klinik, rumah sakit, dan laboratorium",
            imageUrl: "/medical-gloves.jpg",
            slug: "medical",
            features: [
                "Latex & Nitrile tersedia",
                "Powder-free untuk alergi",
                "Ketebalan 4-6 mil",
                "Sertifikat CE & ISO"
            ]
        },
        {
            title: "Sarung Tangan Food Grade",
            description: "Untuk restoran, catering, dan food processing",
            imageUrl: "/food-gloves.jpg",
            slug: "food-service",
            features: [
                "Food safe certified",
                "Polyethylene & Vinyl",
                "Embossed texture",
                "Ekonomis untuk volume besar"
            ]
        },
        {
            title: "Sarung Tangan Industri",
            description: "Untuk manufaktur, automotive, dan konstruksi",
            imageUrl: "/industrial-gloves.jpg",
            slug: "industrial",
            features: [
                "Heavy duty protection",
                "Chemical resistant",
                "Cut level protection",
                "Berbagai ukuran tersedia"
            ]
        }
    ]

    return (
        <section className="bg-white py-24">
            <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                <div className="text-center">
                    <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
                        Kategori Produk Kami
                    </h2>
                    <p className="mt-4 text-xl text-gray-600 max-w-3xl mx-auto">
                        Sarung tangan berkualitas tinggi untuk berbagai kebutuhan industri.
                        Dapatkan harga grosir terbaik dengan minimum order quantity yang fleksibel.
                    </p>
                </div>

                <div className="mt-16 grid gap-8 lg:grid-cols-3">
                    {categories.map((category, index) => (
                        <CategoryCard key={index} {...category} />
                    ))}
                </div>

                {/* CTA Section */}
                <div className="mt-20 text-center">
                    <div className="rounded-2xl bg-gradient-to-r from-blue-600 to-teal-600 p-8 lg:p-12">
                        <h3 className="text-2xl font-bold text-white lg:text-3xl">
                            Tidak Menemukan yang Anda Cari?
                        </h3>
                        <p className="mt-4 text-xl text-blue-100">
                            Tim kami siap membantu Anda menemukan produk yang tepat sesuai kebutuhan spesifik
                        </p>
                        <div className="mt-8 flex flex-col sm:flex-row gap-4 justify-center">
                            <LocalizedClientLink href="/quote-request">
                                <Button
                                    size="large"
                                    variant="transparent"
                                    className="border-white text-white hover:bg-white hover:text-blue-900 px-8"
                                >
                                    Konsultasi Gratis
                                </Button>
                            </LocalizedClientLink>
                            <LocalizedClientLink href="/contact">
                                <Button
                                    size="large"
                                    className="bg-teal-500 hover:bg-teal-600 text-white px-8"
                                >
                                    Hubungi Tim Sales
                                </Button>
                            </LocalizedClientLink>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    )
}

export default GloopiCategories
