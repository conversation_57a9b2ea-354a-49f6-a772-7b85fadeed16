"use client"

import { Heading } from "@medusajs/ui"
import Image from "next/image"
import Link from "next/link"

const industryCategories = [
  {
    id: "medical",
    title: "Medis",
    description: "Sarung tangan untuk rumah sakit, klinik, dan fasilitas kesehatan",
    image: "https://images.unsplash.com/photo-1584308666744-24d5c474f2ae?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80",
    href: "/id/categories/medical-gloves",
    featured: true,
    standards: ["EN 455", "FDA 510(k)"]
  },
  {
    id: "industrial",
    title: "Industri & Pabrik",
    description: "Perlindungan untuk pekerja industri dan manufaktur",
    image: "https://images.unsplash.com/photo-1504328345606-18bbc8c9d7d1?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80",
    href: "/id/categories/industrial-gloves",
    featured: false,
    standards: ["EN 388", "EN 374"]
  },
  {
    id: "restaurant",
    title: "Restoran & Layanan Makanan",
    description: "Sarung tangan food-grade untuk industri kuliner",
    image: "https://images.unsplash.com/photo-**********-f6e7ad7d3136?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80",
    href: "/id/categories/restaurant-gloves",
    featured: false,
    standards: ["EN 1186", "FDA 21 CFR 177"]
  },
  {
    id: "construction",
    title: "Konstruksi",
    description: "Sarung tangan tahan lama untuk proyek konstruksi",
    image: "https://images.unsplash.com/photo-1581094794329-c8112a89af12?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80",
    href: "/id/categories/construction-gloves",
    featured: false,
    standards: ["EN 388", "ANSI/ISEA"]
  }
]

const IndustryCategories = () => {
  return (
    <section className="py-16 px-4 bg-white">
      <div className="max-w-7xl mx-auto">
        <div className="text-center mb-12">
          <Heading level="h2" className="text-3xl font-bold text-gray-900 mb-4">
            Belanja Berdasarkan Industri
          </Heading>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Temukan sarung tangan yang tepat untuk kebutuhan industri Anda dengan standar kualitas internasional
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {industryCategories.map((category) => (
            <Link key={category.id} href={category.href}>
              <div className="group relative overflow-hidden rounded-xl bg-white shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
                <div className="aspect-[4/3] relative">
                  <Image
                    src={category.image}
                    alt={category.title}
                    fill
                    className="object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/20 to-transparent" />
                  
                  {category.featured && (
                    <div className="absolute top-4 left-4">
                      <span className="bg-blue-600 text-white px-3 py-1 rounded-full text-sm font-semibold">
                        Fase 1
                      </span>
                    </div>
                  )}
                </div>
                
                <div className="absolute bottom-0 left-0 right-0 p-6 text-white">
                  <h3 className="text-xl font-bold mb-2">{category.title}</h3>
                  <p className="text-sm text-white/90 mb-3">{category.description}</p>
                  
                  <div className="flex flex-wrap gap-1">
                    {category.standards.map((standard) => (
                      <span
                        key={standard}
                        className="bg-white/20 backdrop-blur-sm text-white px-2 py-1 rounded text-xs"
                      >
                        {standard}
                      </span>
                    ))}
                  </div>
                </div>
              </div>
            </Link>
          ))}
        </div>

        <div className="text-center mt-12">
          <Link href="/id/store">
            <button className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 rounded-lg font-semibold transition-colors">
              Lihat Semua Kategori
            </button>
          </Link>
        </div>
      </div>
    </section>
  )
}

export default IndustryCategories
