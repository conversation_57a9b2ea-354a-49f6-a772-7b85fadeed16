"use client"

import { But<PERSON> } from "@medusajs/ui"
import { ShoppingBag, Users, CheckCircleSolid, ClockSolid } from "@medusajs/icons"
import LocalizedClientLink from "@/modules/common/components/localized-client-link"

const GloopiHero = () => {
    return (
        <div className="relative overflow-hidden bg-gradient-to-br from-blue-900 via-blue-800 to-teal-600">
            {/* Background Pattern */}
            <div className="absolute inset-0 bg-grid-white/10 bg-[size:40px_40px] opacity-20" />

            <div className="relative mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                <div className="pb-16 pt-20 sm:pb-24 sm:pt-24 lg:pb-32 lg:pt-32">
                    <div className="grid lg:grid-cols-2 gap-12 items-center">

                        {/* Content */}
                        <div className="text-center lg:text-left">
                            <h1 className="text-4xl font-bold tracking-tight text-white sm:text-5xl lg:text-6xl">
                                Sarung Tangan Grosir
                                <span className="block text-teal-300">Bersertifikat & Respon Cepat</span>
                            </h1>

                            <p className="mt-6 text-xl text-blue-100 max-w-2xl">
                                Solusi terpercaya untuk kebutuhan sarung tangan dalam jumlah besar.
                                Melayani klinik, restoran, manufaktur, dan reseller di seluruh Indonesia.
                            </p>

                            {/* CTA Buttons */}
                            <div className="mt-10 flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
                                <LocalizedClientLink href="/products">
                                    <Button size="large" className="bg-teal-500 hover:bg-teal-600 text-white px-8">
                                        <ShoppingBag className="mr-2 h-5 w-5" />
                                        Lihat Produk
                                    </Button>
                                </LocalizedClientLink>

                                <LocalizedClientLink href="/quote-request">
                                    <Button
                                        size="large"
                                        variant="transparent"
                                        className="border-white text-white hover:bg-white hover:text-blue-900 px-8"
                                    >
                                        Ajukan Penawaran Sekarang
                                    </Button>
                                </LocalizedClientLink>
                            </div>

                            {/* Trust Indicators */}
                            <div className="mt-10 grid grid-cols-2 sm:grid-cols-4 gap-6 text-center">
                                <div className="flex flex-col items-center">
                                    <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-teal-500/20">
                                        <ClockSolid className="h-6 w-6 text-teal-300" />
                                    </div>
                                    <p className="mt-2 text-sm font-medium text-white">Respon Cepat</p>
                                    <p className="text-xs text-blue-200">&lt; 2 Jam</p>
                                </div>

                                <div className="flex flex-col items-center">
                                    <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-teal-500/20">
                                        <CheckCircleSolid className="h-6 w-6 text-teal-300" />
                                    </div>
                                    <p className="mt-2 text-sm font-medium text-white">Bersertifikat</p>
                                    <p className="text-xs text-blue-200">ISO & CE</p>
                                </div>

                                <div className="flex flex-col items-center">
                                    <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-teal-500/20">
                                        <Users className="h-6 w-6 text-teal-300" />
                                    </div>
                                    <p className="mt-2 text-sm font-medium text-white">1000+ Client</p>
                                    <p className="text-xs text-blue-200">Terpercaya</p>
                                </div>

                                <div className="flex flex-col items-center">
                                    <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-teal-500/20">
                                        <ShoppingBag className="h-6 w-6 text-teal-300" />
                                    </div>
                                    <p className="mt-2 text-sm font-medium text-white">Grosir</p>
                                    <p className="text-xs text-blue-200">Harga Terbaik</p>
                                </div>
                            </div>
                        </div>

                        {/* Image/Visual */}
                        <div className="relative lg:block">
                            <div className="relative mx-auto w-full max-w-lg">
                                {/* Placeholder for product showcase */}
                                <div className="aspect-square rounded-2xl bg-white/10 backdrop-blur-sm p-8">
                                    <div className="h-full w-full rounded-xl bg-gradient-to-br from-white/20 to-white/5 flex items-center justify-center">
                                        <div className="text-center">
                                            <div className="mx-auto h-24 w-24 rounded-full bg-teal-500/30 flex items-center justify-center mb-4">
                                                <ShoppingBag className="h-12 w-12 text-white" />
                                            </div>
                                            <h3 className="text-xl font-semibold text-white mb-2">
                                                Katalog Lengkap
                                            </h3>
                                            <p className="text-blue-200 text-sm">
                                                Sarung tangan medis, industri, dan F&B
                                            </p>
                                        </div>
                                    </div>
                                </div>

                                {/* Floating cards */}
                                <div className="absolute -top-4 -left-4 w-24 h-24 bg-teal-400 rounded-lg shadow-lg flex items-center justify-center transform rotate-12">
                                    <span className="text-white font-bold text-sm">Latex</span>
                                </div>

                                <div className="absolute -bottom-4 -right-4 w-24 h-24 bg-blue-400 rounded-lg shadow-lg flex items-center justify-center transform -rotate-12">
                                    <span className="text-white font-bold text-sm">Nitrile</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    )
}

export default GloopiHero
