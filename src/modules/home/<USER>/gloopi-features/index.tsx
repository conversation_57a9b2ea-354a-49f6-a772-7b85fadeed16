import { CheckCircleSolid, BuildingStorefront, ShoppingBag, DocumentText } from "@medusajs/icons"

interface FeatureCardProps {
    icon: React.ReactNode
    title: string
    description: string
    features: string[]
}

const FeatureCard = ({ icon, title, description, features }: FeatureCardProps) => {
    return (
        <div className="rounded-2xl bg-white p-8 shadow-sm border border-gray-100 hover:shadow-md transition-shadow">
            <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-blue-100">
                {icon}
            </div>

            <h3 className="mt-6 text-xl font-semibold text-gray-900">{title}</h3>
            <p className="mt-2 text-gray-600">{description}</p>

            <ul className="mt-4 space-y-2">
                {features.map((feature, index) => (
                    <li key={index} className="flex items-center text-sm text-gray-600">
                        <CheckCircleSolid className="mr-2 h-4 w-4 text-green-500 flex-shrink-0" />
                        {feature}
                    </li>
                ))}
            </ul>
        </div>
    )
}

const GloopiFeatures = () => {
    const features = [
        {
            icon: <BuildingStorefront className="h-6 w-6 text-blue-600" />,
            title: "Katalog Lengkap",
            description: "Sarung tangan untuk berbagai kebutuhan industri",
            features: [
                "Latex, Nitrile, Vinyl, Polyethylene",
                "Ukuran S, M, L, XL tersedia",
                "Powdered & Powder-free",
                "Berbagai ketebalan (3-6 mil)"
            ]
        },
        {
            icon: <DocumentText className="h-6 w-6 text-blue-600" />,
            title: "Proses RFQ Mudah",
            description: "Request for Quotation yang cepat dan transparan",
            features: [
                "Form permintaan online 24/7",
                "Respon kurang dari 2 jam",
                "Harga transparan tanpa mark-up",
                "Negosiasi via WhatsApp"
            ]
        },
        {
            icon: <ShoppingBag className="h-6 w-6 text-blue-600" />,
            title: "Logistik Fleksibel",
            description: "Pengiriman ke seluruh Indonesia dengan berbagai ekspedisi",
            features: [
                "Pick up sendiri (Surabaya)",
                "Ekspedisi terpercaya (JNE, J&T, dll)",
                "Truk container untuk qty besar",
                "Tracking real-time"
            ]
        }
    ]

    return (
        <section className="bg-gray-50 py-24">
            <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                <div className="text-center">
                    <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
                        Mengapa Pilih Gloopi?
                    </h2>
                    <p className="mt-4 text-xl text-gray-600">
                        Partner terpercaya untuk kebutuhan sarung tangan grosir Anda
                    </p>
                </div>

                <div className="mt-16 grid gap-8 lg:grid-cols-3">
                    {features.map((feature, index) => (
                        <FeatureCard key={index} {...feature} />
                    ))}
                </div>

                {/* Stats Section */}
                <div className="mt-20 rounded-2xl bg-gradient-to-br from-blue-900 to-teal-600 p-8 lg:p-12">
                    <div className="grid gap-8 lg:grid-cols-4 text-center">
                        <div>
                            <div className="text-3xl font-bold text-white lg:text-4xl">1000+</div>
                            <div className="mt-2 text-blue-200">Client Terpuaskan</div>
                        </div>
                        <div>
                            <div className="text-3xl font-bold text-white lg:text-4xl">50+</div>
                            <div className="mt-2 text-blue-200">Jenis Produk</div>
                        </div>
                        <div>
                            <div className="text-3xl font-bold text-white lg:text-4xl">2 Jam</div>
                            <div className="mt-2 text-blue-200">Waktu Respon</div>
                        </div>
                        <div>
                            <div className="text-3xl font-bold text-white lg:text-4xl">10+</div>
                            <div className="mt-2 text-blue-200">Tahun Pengalaman</div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    )
}

export default GloopiFeatures
