"use client"

import { Heading } from "@medusajs/ui"
import { Star } from "@medusajs/icons"

const testimonials = [
  {
    id: 1,
    name: "Dr. <PERSON>",
    role: "<PERSON><PERSON><PERSON> Medis, RS Siloam",
    company: "RS Siloam Jakarta",
    content: "Sarung tangan medis dari G<PERSON>i.id memiliki kualitas yang sangat baik dan sesuai dengan standar internasional. Pengiriman selalu tepat waktu untuk kebutuhan rumah sakit kami.",
    rating: 5,
    image: "/testimonials/doctor-1.jpg"
  },
  {
    id: 2,
    name: "<PERSON><PERSON>",
    role: "<PERSON><PERSON><PERSON>ga<PERSON>",
    company: "PT Indofood Manufacturing",
    content: "Sebagai manajer pengadaan, saya sangat menghargai transparansi harga dan kualitas produk yang konsisten. Platform B2B mereka memudahkan proses pemesanan dalam jumlah besar.",
    rating: 5,
    image: "/testimonials/manager-1.jpg"
  },
  {
    id: 3,
    name: "<PERSON> <PERSON>",
    role: "Head Chef",
    company: "Restaurant Santorini",
    content: "Sarung tangan food-grade dari <PERSON>.id sangat nyaman digunakan dan tidak mempengaruhi cita rasa makanan. Sangat penting untuk menjaga standar kebersihan di dapur kami.",
    rating: 5,
    image: "/testimonials/chef-1.jpg"
  }
]

const Testimonials = () => {
  return (
    <section className="py-16 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4">
        <div className="text-center mb-12">
          <Heading level="h2" className="text-3xl font-bold text-gray-900 mb-4">
            Dipercaya oleh Profesional di Seluruh Indonesia
          </Heading>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Ribuan profesional dari berbagai industri mempercayai Gloopi.id untuk kebutuhan sarung tangan berkualitas tinggi
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {testimonials.map((testimonial) => (
            <div key={testimonial.id} className="bg-white rounded-xl shadow-lg p-6 hover:shadow-xl transition-shadow">
              <div className="flex items-center mb-4">
                {[...Array(testimonial.rating)].map((_, i) => (
                  <Star key={i} className="w-5 h-5 text-yellow-400 fill-current" />
                ))}
              </div>
              
              <blockquote className="text-gray-700 mb-6 italic">
                "{testimonial.content}"
              </blockquote>
              
              <div className="flex items-center">
                <div className="w-12 h-12 bg-gray-200 rounded-full flex items-center justify-center mr-4">
                  <span className="text-gray-600 font-semibold text-lg">
                    {testimonial.name.split(' ').map(n => n[0]).join('')}
                  </span>
                </div>
                <div>
                  <div className="font-semibold text-gray-900">{testimonial.name}</div>
                  <div className="text-sm text-gray-600">{testimonial.role}</div>
                  <div className="text-sm text-blue-600">{testimonial.company}</div>
                </div>
              </div>
            </div>
          ))}
        </div>

        <div className="text-center mt-12">
          <div className="inline-flex items-center space-x-2 text-gray-600">
            <Star className="w-5 h-5 text-yellow-400 fill-current" />
            <span className="font-semibold">4.9/5</span>
            <span>dari 1,200+ ulasan pelanggan</span>
          </div>
        </div>
      </div>
    </section>
  )
}

export default Testimonials
