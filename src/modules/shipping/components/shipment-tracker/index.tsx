"use client"

import { useState } from "react"
import { Button, Input, Container } from "@medusajs/ui"
import { HttpTypes } from "@medusajs/types"
import { toast } from "@medusajs/ui"

interface TrackingEvent {
    id: string
    status: string
    description: string
    location?: string
    event_date: string
    source: "carrier" | "manual" | "system"
}

interface Shipment {
    id: string
    tracking_number: string
    status: string
    carrier_name?: string
    shipped_date?: string
    estimated_delivery?: string
    delivered_date?: string
    shipping_address: any
    tracking_events: TrackingEvent[]
}

const statusIcons = {
    pending: "⏳",
    preparing: "📦",
    packed: "📦",
    shipped: "🚚",
    in_transit: "🚚",
    delivered: "✅",
    failed: "❌",
    returned: "↩️",
}

const statusColors = {
    pending: "text-yellow-600",
    preparing: "text-blue-600",
    packed: "text-blue-600",
    shipped: "text-green-600",
    in_transit: "text-green-600",
    delivered: "text-green-700",
    failed: "text-red-600",
    returned: "text-orange-600",
}

const statusLabels = {
    pending: "<PERSON>unggu",
    preparing: "Dipersiapkan",
    packed: "Dikemas",
    shipped: "Dikirim",
    in_transit: "<PERSON>am <PERSON>",
    delivered: "Diterima",
    failed: "Gagal",
    returned: "Dikembalikan",
}

export default function ShipmentTracker() {
    const [trackingNumber, setTrackingNumber] = useState("")
    const [shipment, setShipment] = useState<Shipment | null>(null)
    const [loading, setLoading] = useState(false)
    const [error, setError] = useState<string | null>(null)

    const handleTrack = async () => {
        if (!trackingNumber.trim()) {
            toast.error("Masukkan nomor resi pengiriman")
            return
        }

        setLoading(true)
        setError(null)

        try {
            const response = await fetch(`/store/shipments/track?tracking_number=${encodeURIComponent(trackingNumber)}`)

            if (!response.ok) {
                if (response.status === 404) {
                    throw new Error("Nomor resi tidak ditemukan")
                }
                throw new Error("Terjadi kesalahan saat melacak pengiriman")
            }

            const data = await response.json()
            setShipment(data.shipment)
        } catch (err) {
            setError(err instanceof Error ? err.message : "Terjadi kesalahan")
            setShipment(null)
        } finally {
            setLoading(false)
        }
    }

    const statusIcon = shipment ? statusIcons[shipment.status as keyof typeof statusIcons] : "⏳"

    return (
        <div className="min-h-screen bg-gray-50 py-8">
            <Container className="max-w-4xl">
                <div className="text-center mb-8">
                    <h1 className="text-3xl font-bold text-gray-900 mb-4">
                        Lacak Pengiriman
                    </h1>
                    <p className="text-lg text-gray-600">
                        Masukkan nomor resi untuk melacak status pengiriman Anda
                    </p>
                </div>

                {/* Tracking Input */}
                <div className="bg-white rounded-lg shadow-sm p-6 mb-8">
                    <div className="flex gap-4">
                        <Input
                            placeholder="Masukkan nomor resi (contoh: JNE123456789)"
                            value={trackingNumber}
                            onChange={(e) => setTrackingNumber(e.target.value)}
                            onKeyPress={(e) => e.key === 'Enter' && handleTrack()}
                            className="flex-1"
                        />
                        <Button
                            onClick={handleTrack}
                            disabled={loading}
                            className="px-8"
                        >
                            {loading ? "Melacak..." : "Lacak"}
                        </Button>
                    </div>
                </div>

                {/* Error Message */}
                {error && (
                    <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-8">
                        <div className="flex items-center">
                            <span className="text-lg mr-2">❌</span>
                            <p className="text-red-800">{error}</p>
                        </div>
                    </div>
                )}

                {/* Shipment Details */}
                {shipment && (
                    <div className="space-y-6">
                        {/* Status Header */}
                        <div className="bg-white rounded-lg shadow-sm p-6">
                            <div className="flex items-center justify-between mb-4">
                                <div className="flex items-center">
                                    <span className={`text-2xl mr-3`}>{statusIcon}</span>
                                    <div>
                                        <h2 className="text-xl font-semibold text-gray-900">
                                            {statusLabels[shipment.status as keyof typeof statusLabels]}
                                        </h2>
                                        <p className="text-gray-600">Nomor Resi: {shipment.tracking_number}</p>
                                    </div>
                                </div>
                                {shipment.carrier_name && (
                                    <div className="text-right">
                                        <p className="text-sm text-gray-500">Kurir</p>
                                        <p className="font-medium">{shipment.carrier_name}</p>
                                    </div>
                                )}
                            </div>

                            {/* Timeline */}
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
                                {shipment.shipped_date && (
                                    <div>
                                        <p className="text-sm text-gray-500">Tanggal Kirim</p>
                                        <p className="font-medium">
                                            {new Date(shipment.shipped_date).toLocaleDateString('id-ID')}
                                        </p>
                                    </div>
                                )}
                                {shipment.estimated_delivery && (
                                    <div>
                                        <p className="text-sm text-gray-500">Estimasi Tiba</p>
                                        <p className="font-medium">
                                            {new Date(shipment.estimated_delivery).toLocaleDateString('id-ID')}
                                        </p>
                                    </div>
                                )}
                                {shipment.delivered_date && (
                                    <div>
                                        <p className="text-sm text-gray-500">Tanggal Diterima</p>
                                        <p className="font-medium text-green-600">
                                            {new Date(shipment.delivered_date).toLocaleDateString('id-ID')}
                                        </p>
                                    </div>
                                )}
                            </div>
                        </div>

                        {/* Tracking Events */}
                        <div className="bg-white rounded-lg shadow-sm p-6">
                            <h3 className="text-lg font-semibold text-gray-900 mb-4">
                                Riwayat Pengiriman
                            </h3>
                            <div className="space-y-4">
                                {shipment.tracking_events.map((event, index) => (
                                    <div key={event.id} className="flex items-start">
                                        <div className="flex-shrink-0 w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center mr-4">
                                            <div className="w-3 h-3 rounded-full bg-blue-600"></div>
                                        </div>
                                        <div className="flex-1">
                                            <div className="flex justify-between items-start">
                                                <div>
                                                    <p className="font-medium text-gray-900">{event.description}</p>
                                                    {event.location && (
                                                        <p className="text-sm text-gray-600">{event.location}</p>
                                                    )}
                                                </div>
                                                <div className="text-right">
                                                    <p className="text-sm text-gray-900">
                                                        {new Date(event.event_date).toLocaleDateString('id-ID', {
                                                            year: 'numeric',
                                                            month: 'short',
                                                            day: 'numeric',
                                                            hour: '2-digit',
                                                            minute: '2-digit'
                                                        })}
                                                    </p>
                                                    <p className="text-xs text-gray-500 capitalize">{event.source}</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </div>

                        {/* Shipping Address */}
                        <div className="bg-white rounded-lg shadow-sm p-6">
                            <h3 className="text-lg font-semibold text-gray-900 mb-4">
                                Alamat Tujuan
                            </h3>
                            <div className="text-gray-600">
                                <p>{shipment.shipping_address?.first_name} {shipment.shipping_address?.last_name}</p>
                                <p>{shipment.shipping_address?.address_1}</p>
                                {shipment.shipping_address?.address_2 && (
                                    <p>{shipment.shipping_address.address_2}</p>
                                )}
                                <p>
                                    {shipment.shipping_address?.city}, {shipment.shipping_address?.province} {shipment.shipping_address?.postal_code}
                                </p>
                                <p>{shipment.shipping_address?.country_code?.toUpperCase()}</p>
                                {shipment.shipping_address?.phone && (
                                    <p className="mt-2">Telepon: {shipment.shipping_address.phone}</p>
                                )}
                            </div>
                        </div>
                    </div>
                )}

                {/* Help Section */}
                <div className="mt-12 bg-blue-50 rounded-lg p-6">
                    <h3 className="text-lg font-semibold text-blue-900 mb-4">
                        Butuh Bantuan?
                    </h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <p className="text-blue-800 mb-2">
                                Jika mengalami kendala dengan pengiriman, hubungi tim customer service kami:
                            </p>
                            <ul className="text-blue-700">
                                <li>📞 WhatsApp: +62 812-3456-7890</li>
                                <li>✉️ Email: <EMAIL></li>
                                <li>🕐 Senin-Jumat: 08:00-17:00 WIB</li>
                            </ul>
                        </div>
                        <div>
                            <p className="text-blue-800 mb-2">Informasi umum:</p>
                            <ul className="text-blue-700 text-sm">
                                <li>• Update tracking biasanya tersedia dalam 2-4 jam</li>
                                <li>• Pengiriman dalam kota 1-2 hari kerja</li>
                                <li>• Pengiriman luar kota 2-5 hari kerja</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </Container>
        </div>
    )
}
