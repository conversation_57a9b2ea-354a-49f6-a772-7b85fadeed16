import { useState } from "react"
import { But<PERSON>, Input, Textarea, Container } from "@medusajs/ui"

interface DocumentUploadProps {
    onSubmit: (file: File, metadata: {
        title: string
        description?: string
        type: string
        order_id?: string
        tags: string[]
    }) => Promise<void>
    onCancel: () => void
    defaultOrderId?: string
}

const documentTypes = [
    { value: "purchase_order", label: "Purchase Order" },
    { value: "npwp", label: "NPWP" },
    { value: "invoice", label: "Invoice" },
    { value: "receipt", label: "<PERSON>wi<PERSON><PERSON>" },
    { value: "other", label: "Lainny<PERSON>" },
]

export function DocumentUpload({
    onSubmit,
    onCancel,
    defaultOrderId
}: DocumentUploadProps) {
    const [formData, setFormData] = useState({
        title: "",
        description: "",
        type: "other",
        order_id: defaultOrderId || "",
        tags: ""
    })
    const [selectedFile, setSelectedFile] = useState<File | null>(null)
    const [loading, setLoading] = useState(false)
    const [dragActive, setDragActive] = useState(false)

    const handleDrag = (e: React.DragEvent) => {
        e.preventDefault()
        e.stopPropagation()
        if (e.type === "dragenter" || e.type === "dragover") {
            setDragActive(true)
        } else if (e.type === "dragleave") {
            setDragActive(false)
        }
    }

    const handleDrop = (e: React.DragEvent) => {
        e.preventDefault()
        e.stopPropagation()
        setDragActive(false)

        if (e.dataTransfer.files && e.dataTransfer.files[0]) {
            handleFileChange(e.dataTransfer.files[0])
        }
    }

    const handleFileInput = (e: React.ChangeEvent<HTMLInputElement>) => {
        if (e.target.files && e.target.files[0]) {
            handleFileChange(e.target.files[0])
        }
    }

    const handleFileChange = (file: File) => {
        setSelectedFile(file)
        // Auto-fill title if empty
        if (!formData.title) {
            setFormData(prev => ({
                ...prev,
                title: file.name.split('.')[0]
            }))
        }
    }

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault()

        if (!selectedFile || !formData.title) {
            return
        }

        setLoading(true)
        try {
            const tags = formData.tags.split(',').map(tag => tag.trim()).filter(tag => tag.length > 0)

            await onSubmit(selectedFile, {
                title: formData.title,
                description: formData.description || undefined,
                type: formData.type,
                order_id: formData.order_id || undefined,
                tags
            })
        } catch (error) {
            console.error("Error uploading document:", error)
        } finally {
            setLoading(false)
        }
    }

    const formatFileSize = (bytes: number) => {
        const sizes = ["Bytes", "KB", "MB", "GB"]
        const i = Math.floor(Math.log(bytes) / Math.log(1024))
        return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + " " + sizes[i]
    }

    return (
        <Container className="max-w-2xl p-6 bg-white rounded-lg border">
            <div className="mb-6">
                <h2 className="text-xl font-semibold text-gray-900">Unggah Dokumen</h2>
                <p className="text-gray-600 mt-2">
                    Unggah dokumen terkait pesanan atau akun Anda
                </p>
            </div>

            <form onSubmit={handleSubmit} className="space-y-6">
                {/* File Upload Area */}
                <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                        File Dokumen *
                    </label>
                    <div
                        className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${dragActive
                                ? "border-blue-500 bg-blue-50"
                                : "border-gray-300 hover:border-gray-400"
                            }`}
                        onDragEnter={handleDrag}
                        onDragLeave={handleDrag}
                        onDragOver={handleDrag}
                        onDrop={handleDrop}
                    >
                        {selectedFile ? (
                            <div className="space-y-2">
                                <div className="text-2xl">📄</div>
                                <div className="font-medium text-gray-900">{selectedFile.name}</div>
                                <div className="text-sm text-gray-500">
                                    {formatFileSize(selectedFile.size)} • {selectedFile.type}
                                </div>
                                <Button
                                    type="button"
                                    variant="secondary"
                                    size="small"
                                    onClick={() => setSelectedFile(null)}
                                >
                                    Ganti File
                                </Button>
                            </div>
                        ) : (
                            <div className="space-y-4">
                                <div className="text-2xl">📁</div>
                                <div>
                                    <p className="text-gray-600 mb-2">
                                        Seret file ke sini atau klik untuk memilih
                                    </p>
                                    <input
                                        type="file"
                                        id="file-upload"
                                        className="hidden"
                                        onChange={handleFileInput}
                                        accept=".pdf,.doc,.docx,.xls,.xlsx,.jpg,.jpeg,.png,.webp"
                                    />
                                    <Button
                                        type="button"
                                        variant="secondary"
                                        onClick={() => document.getElementById('file-upload')?.click()}
                                    >
                                        Pilih File
                                    </Button>
                                </div>
                                <p className="text-xs text-gray-500">
                                    Format didukung: PDF, DOC, DOCX, XLS, XLSX, JPG, PNG, WEBP (Maksimal 10MB)
                                </p>
                            </div>
                        )}
                    </div>
                </div>

                {/* Document Details */}
                <div className="space-y-4">
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                            Judul Dokumen *
                        </label>
                        <Input
                            required
                            placeholder="Masukkan judul dokumen"
                            value={formData.title}
                            onChange={(e) => setFormData(prev => ({
                                ...prev,
                                title: e.target.value
                            }))}
                        />
                    </div>

                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                            Tipe Dokumen
                        </label>
                        <select
                            value={formData.type}
                            onChange={(e) => setFormData(prev => ({
                                ...prev,
                                type: e.target.value
                            }))}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        >
                            {documentTypes.map((type) => (
                                <option key={type.value} value={type.value}>
                                    {type.label}
                                </option>
                            ))}
                        </select>
                    </div>

                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                            Deskripsi
                        </label>
                        <Textarea
                            placeholder="Deskripsi tambahan (opsional)"
                            value={formData.description}
                            onChange={(e) => setFormData(prev => ({
                                ...prev,
                                description: e.target.value
                            }))}
                            rows={3}
                        />
                    </div>

                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                            Order ID
                        </label>
                        <Input
                            placeholder="ID pesanan terkait (opsional)"
                            value={formData.order_id}
                            onChange={(e) => setFormData(prev => ({
                                ...prev,
                                order_id: e.target.value
                            }))}
                        />
                    </div>

                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                            Tag
                        </label>
                        <Input
                            placeholder="Tag dipisahkan koma (contoh: penting, kontrak)"
                            value={formData.tags}
                            onChange={(e) => setFormData(prev => ({
                                ...prev,
                                tags: e.target.value
                            }))}
                        />
                        <p className="text-xs text-gray-500 mt-1">
                            Pisahkan beberapa tag dengan koma
                        </p>
                    </div>
                </div>

                <div className="flex justify-end gap-3 pt-4">
                    <Button variant="secondary" onClick={onCancel} disabled={loading}>
                        Batal
                    </Button>
                    <Button
                        type="submit"
                        disabled={loading || !selectedFile || !formData.title}
                    >
                        {loading ? "Mengunggah..." : "Unggah Dokumen"}
                    </Button>
                </div>
            </form>
        </Container>
    )
}
