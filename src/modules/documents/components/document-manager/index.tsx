import { useState } from "react"
import { Button, Input, Container } from "@medusajs/ui"

interface Document {
    id: string
    type: string
    title: string
    description?: string
    file_name: string
    file_url: string
    file_size?: number
    mime_type?: string
    version: number
    uploaded_at: string
    tags: string[]
}

interface DocumentManagerProps {
    documents: Document[]
    onUploadDocument?: (file: File, metadata: {
        title: string
        description?: string
        type: string
        order_id?: string
        tags: string[]
    }) => Promise<void>
    onDownloadDocument: (document: Document) => void
    readonly?: boolean
}

const documentTypes = [
    { value: "purchase_order", label: "Purchase Order" },
    { value: "npwp", label: "NPWP" },
    { value: "invoice", label: "Invoice" },
    { value: "receipt", label: "<PERSON>wi<PERSON><PERSON>" },
    { value: "tax_invoice", label: "Faktur Pajak" },
    { value: "contract", label: "Kontrak" },
    { value: "other", label: "<PERSON>nny<PERSON>" },
]

export function DocumentManager({
    documents,
    onUploadDocument,
    onDownloadDocument,
    readonly = false
}: DocumentManagerProps) {
    const [searchTerm, setSearchTerm] = useState("")
    const [typeFilter, setTypeFilter] = useState("all")
    const [showUploadForm, setShowUploadForm] = useState(false)

    const filteredDocuments = documents.filter(document => {
        const matchesSearch =
            document.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
            document.file_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
            document.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))

        const matchesType = typeFilter === "all" || document.type === typeFilter

        return matchesSearch && matchesType
    })

    const formatFileSize = (bytes?: number) => {
        if (!bytes) return "-"
        const sizes = ["Bytes", "KB", "MB", "GB"]
        const i = Math.floor(Math.log(bytes) / Math.log(1024))
        return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + " " + sizes[i]
    }

    const getFileTypeIcon = (mimeType?: string) => {
        if (!mimeType) return "📄"
        if (mimeType.includes("pdf")) return "📕"
        if (mimeType.includes("image")) return "🖼️"
        if (mimeType.includes("word")) return "📘"
        if (mimeType.includes("excel") || mimeType.includes("sheet")) return "📊"
        return "📄"
    }

    const groupDocumentsByType = (docs: Document[]) => {
        const grouped: Record<string, Document[]> = {}
        docs.forEach(doc => {
            if (!grouped[doc.type]) {
                grouped[doc.type] = []
            }
            grouped[doc.type].push(doc)
        })
        return grouped
    }

    const groupedDocuments = groupDocumentsByType(filteredDocuments)

    return (
        <div className="space-y-6">
            {/* Header */}
            <div className="flex justify-between items-center">
                <div>
                    <h2 className="text-xl font-semibold text-gray-900">Dokumen</h2>
                    <p className="text-gray-600">Kelola dokumen terkait pesanan dan akun Anda</p>
                </div>
                {!readonly && onUploadDocument && (
                    <Button onClick={() => setShowUploadForm(true)}>
                        + Unggah Dokumen
                    </Button>
                )}
            </div>

            {/* Search and Filter */}
            <Container className="p-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <Input
                        placeholder="Cari berdasarkan judul, nama file, atau tag..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                    />
                    <select
                        value={typeFilter}
                        onChange={(e) => setTypeFilter(e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                        <option value="all">Semua Tipe</option>
                        {documentTypes.map((type) => (
                            <option key={type.value} value={type.value}>
                                {type.label}
                            </option>
                        ))}
                    </select>
                    <div className="text-sm text-gray-600 flex items-center">
                        Total: {filteredDocuments.length} dokumen
                    </div>
                </div>
            </Container>

            {/* Document Groups */}
            <div className="space-y-6">
                {Object.keys(groupedDocuments).length > 0 ? (
                    Object.entries(groupedDocuments).map(([type, docs]) => (
                        <Container key={type} className="p-6">
                            <div className="flex items-center justify-between mb-4">
                                <h3 className="text-lg font-medium text-gray-900">
                                    {documentTypes.find(t => t.value === type)?.label || type} ({docs.length})
                                </h3>
                            </div>

                            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                                {docs.map((document) => (
                                    <div key={document.id} className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                                        <div className="flex items-start justify-between mb-3">
                                            <div className="flex items-center gap-3">
                                                <span className="text-2xl">{getFileTypeIcon(document.mime_type)}</span>
                                                <div className="flex-1">
                                                    <h4 className="font-medium text-gray-900 line-clamp-2">{document.title}</h4>
                                                    <p className="text-sm text-gray-500">{document.file_name}</p>
                                                </div>
                                            </div>
                                        </div>

                                        {document.description && (
                                            <p className="text-sm text-gray-600 mb-3 line-clamp-2">
                                                {document.description}
                                            </p>
                                        )}

                                        <div className="flex items-center justify-between text-xs text-gray-500 mb-3">
                                            <span>{formatFileSize(document.file_size)}</span>
                                            <span>v{document.version}</span>
                                            <span>{new Date(document.uploaded_at).toLocaleDateString('id-ID')}</span>
                                        </div>

                                        {document.tags.length > 0 && (
                                            <div className="flex flex-wrap gap-1 mb-3">
                                                {document.tags.slice(0, 3).map((tag, index) => (
                                                    <span
                                                        key={index}
                                                        className="inline-block bg-gray-100 text-gray-600 text-xs px-2 py-1 rounded"
                                                    >
                                                        {tag}
                                                    </span>
                                                ))}
                                                {document.tags.length > 3 && (
                                                    <span className="text-xs text-gray-500">+{document.tags.length - 3}</span>
                                                )}
                                            </div>
                                        )}

                                        <Button
                                            variant="secondary"
                                            size="small"
                                            onClick={() => onDownloadDocument(document)}
                                            className="w-full"
                                        >
                                            Unduh
                                        </Button>
                                    </div>
                                ))}
                            </div>
                        </Container>
                    ))
                ) : (
                    <Container className="p-12 text-center">
                        <div className="h-12 w-12 bg-gray-100 rounded-full mx-auto mb-4 flex items-center justify-center">
                            <span className="text-2xl">📄</span>
                        </div>
                        <h3 className="text-lg font-medium text-gray-900 mb-2">
                            Tidak ada dokumen ditemukan
                        </h3>
                        <p className="text-gray-600 mb-4">
                            {searchTerm || typeFilter !== "all"
                                ? "Coba ubah filter pencarian Anda"
                                : "Belum ada dokumen yang tersedia"
                            }
                        </p>
                        {!readonly && onUploadDocument && !searchTerm && typeFilter === "all" && (
                            <Button onClick={() => setShowUploadForm(true)}>
                                Unggah Dokumen Pertama
                            </Button>
                        )}
                    </Container>
                )}
            </div>
        </div>
    )
}
