"use client"

import { useState } from "react"
import { Button, Input, Textarea } from "@medusajs/ui"
import { DocumentText, CurrencyDollar } from "@medusajs/icons"

interface QuoteRequestItem {
  productId: string
  productName: string
  quantity: number
  specifications?: string
}

interface GloopiQuoteRequestProps {
  onSubmit: (quoteData: any) => void
  prefilledItems?: QuoteRequestItem[]
}

const GloopiQuoteRequest = ({ onSubmit, prefilledItems = [] }: GloopiQuoteRequestProps) => {
  const [formData, setFormData] = useState({
    companyName: "",
    contactPerson: "",
    email: "",
    phone: "",
    industry: "",
    urgency: "standard",
    deliveryAddress: "",
    specialRequirements: "",
    items: prefilledItems.length > 0 ? prefilledItems : [
      { productId: "", productName: "", quantity: 0, specifications: "" }
    ]
  })

  const [isSubmitting, setIsSubmitting] = useState(false)

  const addItem = () => {
    setFormData(prev => ({
      ...prev,
      items: [...prev.items, { productId: "", productName: "", quantity: 0, specifications: "" }]
    }))
  }

  const removeItem = (index: number) => {
    setFormData(prev => ({
      ...prev,
      items: prev.items.filter((_, i) => i !== index)
    }))
  }

  const updateItem = (index: number, field: keyof QuoteRequestItem, value: any) => {
    setFormData(prev => ({
      ...prev,
      items: prev.items.map((item, i) => 
        i === index ? { ...item, [field]: value } : item
      )
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)
    
    try {
      await onSubmit(formData)
      // Reset form or show success message
    } catch (error) {
      console.error("Error submitting quote request:", error)
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <div className="max-w-4xl mx-auto bg-white rounded-xl shadow-lg border border-gray-200">
      <div className="bg-gradient-to-r from-blue-600 to-indigo-600 text-white p-6 rounded-t-xl">
        <div className="flex items-center mb-2">
          <DocumentText className="w-6 h-6 mr-3" />
          <h2 className="text-2xl font-bold">Permintaan Penawaran B2B</h2>
        </div>
        <p className="text-blue-100">
          Dapatkan penawaran khusus untuk kebutuhan sarung tangan dalam jumlah besar
        </p>
      </div>

      <form onSubmit={handleSubmit} className="p-6 space-y-8">
        {/* Company Information */}
        <div>
          <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <CurrencyDollar className="w-5 h-5 mr-2 text-blue-600" />
            Informasi Perusahaan
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Nama Perusahaan *
              </label>
              <Input
                required
                value={formData.companyName}
                onChange={(e) => setFormData(prev => ({ ...prev, companyName: e.target.value }))}
                placeholder="PT. Contoh Perusahaan"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Industri *
              </label>
              <select
                required
                value={formData.industry}
                onChange={(e) => setFormData(prev => ({ ...prev, industry: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">Pilih Industri</option>
                <option value="medical">Medis & Kesehatan</option>
                <option value="industrial">Industri & Manufaktur</option>
                <option value="restaurant">Restoran & Layanan Makanan</option>
                <option value="construction">Konstruksi & Bangunan</option>
                <option value="other">Lainnya</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Nama Kontak *
              </label>
              <Input
                required
                value={formData.contactPerson}
                onChange={(e) => setFormData(prev => ({ ...prev, contactPerson: e.target.value }))}
                placeholder="John Doe"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Email *
              </label>
              <Input
                type="email"
                required
                value={formData.email}
                onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
                placeholder="<EMAIL>"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Nomor Telepon *
              </label>
              <Input
                required
                value={formData.phone}
                onChange={(e) => setFormData(prev => ({ ...prev, phone: e.target.value }))}
                placeholder="+62 21 1234 5678"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Tingkat Urgensi
              </label>
              <select
                value={formData.urgency}
                onChange={(e) => setFormData(prev => ({ ...prev, urgency: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="standard">Standard (3-5 hari kerja)</option>
                <option value="urgent">Urgent (1-2 hari kerja)</option>
                <option value="immediate">Segera (dalam 24 jam)</option>
              </select>
            </div>
          </div>
        </div>

        {/* Product Items */}
        <div>
          <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <DocumentText className="w-5 h-5 mr-2 text-blue-600" />
            Produk yang Diminta
          </h3>
          <div className="space-y-4">
            {formData.items.map((item, index) => (
              <div key={index} className="bg-gray-50 rounded-lg p-4 border border-gray-200">
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <div className="md:col-span-2">
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Nama Produk *
                    </label>
                    <Input
                      required
                      value={item.productName}
                      onChange={(e) => updateItem(index, "productName", e.target.value)}
                      placeholder="Sarung Tangan Nitril Pemeriksaan"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Jumlah (boxes) *
                    </label>
                    <Input
                      type="number"
                      required
                      min="1"
                      value={item.quantity}
                      onChange={(e) => updateItem(index, "quantity", parseInt(e.target.value))}
                      placeholder="100"
                    />
                  </div>
                  <div className="flex items-end">
                    {formData.items.length > 1 && (
                      <Button
                        type="button"
                        variant="danger"
                        onClick={() => removeItem(index)}
                        className="w-full"
                      >
                        Hapus
                      </Button>
                    )}
                  </div>
                </div>
                <div className="mt-4">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Spesifikasi Khusus (opsional)
                  </label>
                  <Input
                    value={item.specifications || ""}
                    onChange={(e) => updateItem(index, "specifications", e.target.value)}
                    placeholder="Ukuran M, warna biru, bebas bedak"
                  />
                </div>
              </div>
            ))}
          </div>
          <Button
            type="button"
            variant="secondary"
            onClick={addItem}
            className="mt-4"
          >
            + Tambah Produk
          </Button>
        </div>

        {/* Delivery Information */}
        <div>
          <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <svg className="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            Informasi Pengiriman
          </h3>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Alamat Pengiriman *
              </label>
              <Textarea
                required
                value={formData.deliveryAddress}
                onChange={(e) => setFormData(prev => ({ ...prev, deliveryAddress: e.target.value }))}
                placeholder="Jl. Contoh No. 123, Jakarta Selatan, DKI Jakarta 12345"
                rows={3}
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Persyaratan Khusus (opsional)
              </label>
              <Textarea
                value={formData.specialRequirements}
                onChange={(e) => setFormData(prev => ({ ...prev, specialRequirements: e.target.value }))}
                placeholder="Contoh: Pengiriman harus dilakukan pada hari kerja, butuh sertifikat halal, dll."
                rows={3}
              />
            </div>
          </div>
        </div>

        {/* Benefits Info */}
        <div className="bg-blue-50 rounded-lg p-6 border border-blue-200">
          <h4 className="font-semibold text-blue-900 mb-3">Keuntungan Quote B2B:</h4>
          <ul className="space-y-2 text-sm text-blue-800">
            <li className="flex items-center">
              <span className="w-2 h-2 bg-blue-600 rounded-full mr-3"></span>
              Harga khusus untuk pembelian dalam jumlah besar
            </li>
            <li className="flex items-center">
              <span className="w-2 h-2 bg-blue-600 rounded-full mr-3"></span>
              Terms pembayaran fleksibel (Net 30/60 hari)
            </li>
            <li className="flex items-center">
              <span className="w-2 h-2 bg-blue-600 rounded-full mr-3"></span>
              Konsultasi gratis dengan tim ahli produk
            </li>
            <li className="flex items-center">
              <span className="w-2 h-2 bg-blue-600 rounded-full mr-3"></span>
              Pengiriman terjadwal dan prioritas
            </li>
          </ul>
        </div>

        {/* Submit Button */}
        <div className="flex justify-end">
          <Button
            type="submit"
            disabled={isSubmitting}
            className="bg-blue-600 hover:bg-blue-700 px-8 py-3"
          >
            {isSubmitting ? "Mengirim..." : "Kirim Permintaan Quote"}
          </Button>
        </div>
      </form>
    </div>
  )
}

export default GloopiQuoteRequest
