"use client"

import { Heading } from "@medusajs/ui"
import { BookOpen, DocumentText, QuestionMarkCircle, AcademicCapSolid } from "@medusajs/icons"
import Link from "next/link"

const resources = [
  {
    id: "glove-selection-guide",
    title: "Panduan Pemilihan Sarung Tangan",
    description: "Panduan lengkap memilih sarung tangan yang tepat untuk setiap industri dan aplikasi",
    category: "Panduan",
    icon: BookOpen,
    href: "/resources/glove-selection-guide",
    featured: true
  },
  {
    id: "en-standards-faq",
    title: "FAQ Standar EN (European Norm)",
    description: "Penjelasan lengkap tentang standar EN 455, EN 388, EN 374, dan <PERSON> 1186",
    category: "Standar",
    icon: QuestionMarkCircle,
    href: "/resources/en-standards-faq",
    featured: true
  },
  {
    id: "fda-regulations",
    title: "Penjelasan Standar FDA",
    description: "Memahami regulasi FDA 510(k) dan 21 CFR 177 untuk produk medis dan food-grade",
    category: "Standar",
    icon: DocumentText,
    href: "/resources/fda-regulations",
    featured: true
  },
  {
    id: "material-comparison",
    title: "Perbandingan Material Sarung Tangan",
    description: "Nitril vs Lateks vs Vinyl - kelebihan dan kekurangan setiap material",
    category: "Edukasi",
    icon: AcademicCapSolid,
    href: "/resources/material-comparison",
    featured: false
  },
  {
    id: "safety-protocols",
    title: "Protokol Keselamatan Industri",
    description: "Best practices penggunaan sarung tangan di berbagai lingkungan kerja",
    category: "Keselamatan",
    icon: BookOpen,
    href: "/resources/safety-protocols",
    featured: false
  },
  {
    id: "maintenance-care",
    title: "Perawatan & Penyimpanan",
    description: "Cara merawat dan menyimpan sarung tangan untuk menjaga kualitas",
    category: "Perawatan",
    icon: DocumentText,
    href: "/resources/maintenance-care",
    featured: false
  }
]

const categories = [
  { name: "Semua", value: "all" },
  { name: "Panduan", value: "Panduan" },
  { name: "Standar", value: "Standar" },
  { name: "Edukasi", value: "Edukasi" },
  { name: "Keselamatan", value: "Keselamatan" },
  { name: "Perawatan", value: "Perawatan" }
]

const ResourceCenter = () => {
  return (
    <section className="py-16 bg-white">
      <div className="max-w-7xl mx-auto px-4">
        <div className="text-center mb-12">
          <Heading level="h2" className="text-3xl font-bold text-gray-900 mb-4">
            Pusat Sumber Daya
          </Heading>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Pelajari lebih lanjut tentang standar industri, panduan pemilihan, dan best practices 
            penggunaan sarung tangan untuk berbagai aplikasi
          </p>
        </div>

        {/* Featured Resources */}
        <div className="mb-12">
          <h3 className="text-xl font-semibold text-gray-900 mb-6">Panduan Utama</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {resources.filter(resource => resource.featured).map((resource) => {
              const IconComponent = resource.icon
              return (
                <Link key={resource.id} href={resource.href}>
                  <div className="group bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl p-6 border border-blue-100 hover:shadow-lg transition-all duration-300 hover:-translate-y-1">
                    <div className="flex items-center mb-4">
                      <div className="w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center mr-4">
                        <IconComponent className="w-6 h-6 text-white" />
                      </div>
                      <span className="text-xs font-semibold text-blue-600 uppercase tracking-wide">
                        {resource.category}
                      </span>
                    </div>
                    <h4 className="text-lg font-semibold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors">
                      {resource.title}
                    </h4>
                    <p className="text-gray-600 text-sm">
                      {resource.description}
                    </p>
                  </div>
                </Link>
              )
            })}
          </div>
        </div>

        {/* All Resources */}
        <div>
          <h3 className="text-xl font-semibold text-gray-900 mb-6">Semua Sumber Daya</h3>
          
          {/* Category Filter */}
          <div className="flex flex-wrap gap-2 mb-6">
            {categories.map((category) => (
              <button
                key={category.value}
                className="px-4 py-2 rounded-full border border-gray-200 text-sm font-medium hover:border-blue-500 hover:text-blue-600 transition-colors"
              >
                {category.name}
              </button>
            ))}
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {resources.map((resource) => {
              const IconComponent = resource.icon
              return (
                <Link key={resource.id} href={resource.href}>
                  <div className="group bg-white rounded-lg border border-gray-200 p-6 hover:shadow-md transition-all duration-300">
                    <div className="flex items-start justify-between mb-4">
                      <div className="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center group-hover:bg-blue-100 transition-colors">
                        <IconComponent className="w-5 h-5 text-gray-600 group-hover:text-blue-600 transition-colors" />
                      </div>
                      <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">
                        {resource.category}
                      </span>
                    </div>
                    <h4 className="text-lg font-semibold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors">
                      {resource.title}
                    </h4>
                    <p className="text-gray-600 text-sm">
                      {resource.description}
                    </p>
                  </div>
                </Link>
              )
            })}
          </div>
        </div>

        {/* CTA Section */}
        <div className="mt-16 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-2xl p-8 text-center text-white">
          <h3 className="text-2xl font-bold mb-4">
            Butuh Konsultasi Khusus?
          </h3>
          <p className="text-blue-100 mb-6 max-w-2xl mx-auto">
            Tim ahli kami siap membantu Anda memilih sarung tangan yang tepat untuk kebutuhan spesifik industri Anda
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/contact">
              <button className="bg-white text-blue-600 px-6 py-3 rounded-lg font-semibold hover:bg-blue-50 transition-colors">
                Hubungi Tim Ahli
              </button>
            </Link>
            <Link href="/resources/consultation-request">
              <button className="border border-white text-white px-6 py-3 rounded-lg font-semibold hover:bg-white/10 transition-colors">
                Request Konsultasi
              </button>
            </Link>
          </div>
        </div>
      </div>
    </section>
  )
}

export default ResourceCenter
