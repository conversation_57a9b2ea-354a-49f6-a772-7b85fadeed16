import { IconProps } from "types/icon"

const FilePlus = ({ size = "12", ...props }: IconProps) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={size}
      height={size}
      viewBox="0 0 12 12"
      fill="none"
      {...props}
    >
      <g clipPath="url(#clip0_21037_2683)">
        <path
          d="M10.428 2.867L8.133 0.573C7.77 0.209 7.266 0 6.751 0H3.75C2.233 0 1 1.233 1 2.75V4.844C1.413 4.331 2.042 4 2.75 4C3.91 4 4.868 4.883 4.987 6.012C6.117 6.131 7 7.089 7 8.25C7 9.411 6.117 10.369 4.987 10.488C4.922 11.101 4.606 11.634 4.149 12H8.249C9.766 12 10.999 10.767 10.999 9.25V4.249C10.999 3.728 10.796 3.237 10.427 2.867H10.428ZM7 4V1.585C7.024 1.601 7.051 1.612 7.071 1.633L9.365 3.926C9.386 3.947 9.397 3.975 9.414 3.999H6.999L7 4Z"
          fill="#52525B"
        />
        <path
          d="M4.75 7.5H3.5V6.25C3.5 5.836 3.164 5.5 2.75 5.5C2.336 5.5 2 5.836 2 6.25V7.5H0.75C0.336 7.5 0 7.836 0 8.25C0 8.664 0.336 9 0.75 9H2V10.25C2 10.664 2.336 11 2.75 11C3.164 11 3.5 10.664 3.5 10.25V9H4.75C5.164 9 5.5 8.664 5.5 8.25C5.5 7.836 5.164 7.5 4.75 7.5Z"
          fill="#52525B"
        />
      </g>
      <defs>
        <clipPath id="clip0_21037_2683">
          <rect width="12" height="12" fill="white" />
        </clipPath>
      </defs>
    </svg>
  )
}

export default FilePlus
