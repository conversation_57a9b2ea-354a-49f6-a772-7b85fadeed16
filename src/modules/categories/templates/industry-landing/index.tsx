"use client"

import { Heading } from "@medusajs/ui"
import { CheckCircleSolid, InformationCircleSolid } from "@medusajs/icons"
import GloopiProductCard from "@/modules/products/components/gloopi-product-card"
import { GloveProduct, ComplianceStandard } from "@/types/gloves"

interface IndustryLandingProps {
  industry: "medical" | "industrial" | "restaurant" | "construction"
  products: GloveProduct[]
  isB2BCustomer?: boolean
}

const industryConfig = {
  medical: {
    title: "Sarung Tangan Medis",
    subtitle: "Perlindungan Terpercaya untuk Tenaga Kesehatan",
    description: "Koleksi lengkap sarung tangan medis dengan standar internasional EN 455 dan FDA 510(k). Dari pemeriksaan rutin hingga prosedur bedah kompleks.",
    heroImage: "https://images.unsplash.com/photo-1584308666744-24d5c474f2ae?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80",
    keyFeatures: [
      "Steril dan non-steril tersedia",
      "Bebas bedak dan hypoallergenic",
      "Sensitivitas taktil superior",
      "Tahan tusukan dan sobek",
      "Berbagai ukuran presisi"
    ],
    applications: [
      "Pemeriksaan medis rutin",
      "Prosedur bedah",
      "Perawatan intensif",
      "Laboratorium medis",
      "Dental dan ortodonti"
    ],
    compliance: [
      "EN 455 - Standar sarung tangan medis Eropa",
      "FDA 510(k) - Clearance perangkat medis FDA",
      "ISO 13485 - Sistem manajemen kualitas medis"
    ],
    phase: 1
  },
  industrial: {
    title: "Sarung Tangan Industri",
    subtitle: "Perlindungan Maksimal untuk Pekerja Industri",
    description: "Sarung tangan industri dengan perlindungan terhadap risiko mekanis, kimia, dan termal sesuai standar EN 388 dan EN 374.",
    heroImage: "https://images.unsplash.com/photo-1504328345606-18bbc8c9d7d1?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80",
    keyFeatures: [
      "Tahan potong level tinggi",
      "Perlindungan kimia",
      "Tahan panas dan dingin",
      "Grip superior",
      "Daya tahan tinggi"
    ],
    applications: [
      "Manufaktur dan assembly",
      "Penanganan bahan kimia",
      "Welding dan metalwork",
      "Automotive industry",
      "Oil & gas operations"
    ],
    compliance: [
      "EN 388 - Perlindungan terhadap risiko mekanis",
      "EN 374 - Perlindungan terhadap kimia dan mikroorganisme",
      "ANSI/ISEA 105 - Standar sarung tangan Amerika"
    ],
    phase: 2
  },
  restaurant: {
    title: "Sarung Tangan Layanan Makanan",
    subtitle: "Keamanan Pangan dan Kebersihan Optimal",
    description: "Sarung tangan food-grade yang aman untuk kontak langsung dengan makanan, sesuai standar EN 1186 dan FDA 21 CFR 177.",
    heroImage: "/industries/restaurant-hero.jpg",
    keyFeatures: [
      "Food-grade certified",
      "Bebas allergen",
      "Tahan suhu ekstrem",
      "Tidak mengubah rasa",
      "Mudah digunakan"
    ],
    applications: [
      "Persiapan makanan",
      "Food service",
      "Catering dan events",
      "Food processing",
      "Bakery dan pastry"
    ],
    compliance: [
      "EN 1186 - Kontak dengan bahan makanan",
      "FDA 21 CFR 177 - Indirect food additives",
      "HACCP compliant"
    ],
    phase: 3
  },
  construction: {
    title: "Sarung Tangan Konstruksi",
    subtitle: "Ketahanan dan Perlindungan untuk Proyek Berat",
    description: "Sarung tangan konstruksi dengan daya tahan tinggi dan perlindungan terhadap benturan, cuaca, dan abrasi.",
    heroImage: "/industries/construction-hero.jpg",
    keyFeatures: [
      "Impact protection",
      "Weather resistant",
      "High durability",
      "Enhanced grip",
      "Reinforced areas"
    ],
    applications: [
      "Construction sites",
      "Heavy machinery",
      "Demolition work",
      "Electrical work",
      "Plumbing dan HVAC"
    ],
    compliance: [
      "EN 388 - Mechanical risks protection",
      "ANSI/ISEA 138 - Impact protection",
      "OSHA compliant"
    ],
    phase: 4
  }
}

const IndustryLanding = ({ industry, products, isB2BCustomer = false }: IndustryLandingProps) => {
  const config = industryConfig[industry]

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <div className="relative bg-gradient-to-r from-blue-900 to-blue-700 text-white">
        <div className="absolute inset-0 bg-black/20" />
        <div className="relative max-w-7xl mx-auto px-4 py-16">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <div className="flex items-center mb-4">
                <span className="bg-blue-500 text-white px-3 py-1 rounded-full text-sm font-semibold mr-3">
                  Fase {config.phase}
                </span>
                {config.phase === 1 && (
                  <span className="bg-green-500 text-white px-3 py-1 rounded-full text-sm font-semibold">
                    Tersedia Sekarang
                  </span>
                )}
              </div>
              
              <Heading level="h1" className="text-4xl lg:text-5xl font-bold mb-4">
                {config.title}
              </Heading>
              
              <p className="text-xl text-blue-100 mb-6">
                {config.subtitle}
              </p>
              
              <p className="text-blue-50 mb-8 leading-relaxed">
                {config.description}
              </p>
              
              <div className="flex flex-col sm:flex-row gap-4">
                <button className="bg-white text-blue-700 px-8 py-3 rounded-lg font-semibold hover:bg-blue-50 transition-colors">
                  Lihat Produk
                </button>
                <button className="border border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white/10 transition-colors">
                  Konsultasi Gratis
                </button>
              </div>
            </div>
            
            <div className="relative">
              <div className="aspect-[4/3] rounded-xl overflow-hidden shadow-2xl">
                <img
                  src={config.heroImage}
                  alt={config.title}
                  className="w-full h-full object-cover"
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Key Features */}
      <div className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            <div>
              <h2 className="text-3xl font-bold text-gray-900 mb-6">
                Keunggulan Produk
              </h2>
              <ul className="space-y-4">
                {config.keyFeatures.map((feature, index) => (
                  <li key={index} className="flex items-start">
                    <CheckCircleSolid className="w-6 h-6 text-green-600 mr-3 mt-0.5 flex-shrink-0" />
                    <span className="text-gray-700">{feature}</span>
                  </li>
                ))}
              </ul>
            </div>
            
            <div>
              <h2 className="text-3xl font-bold text-gray-900 mb-6">
                Aplikasi Utama
              </h2>
              <ul className="space-y-4">
                {config.applications.map((application, index) => (
                  <li key={index} className="flex items-start">
                    <CheckCircleSolid className="w-6 h-6 text-blue-600 mr-3 mt-0.5 flex-shrink-0" />
                    <span className="text-gray-700">{application}</span>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </div>
      </div>

      {/* Compliance Standards */}
      <div className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Standar Kepatuhan Internasional
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Semua produk kami memenuhi standar kualitas dan keamanan internasional yang ketat
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {config.compliance.map((standard, index) => (
              <div key={index} className="bg-white rounded-lg p-6 shadow-md">
                <div className="flex items-start">
                  <InformationCircleSolid className="w-6 h-6 text-blue-600 mr-3 mt-1 flex-shrink-0" />
                  <div>
                    <h3 className="font-semibold text-gray-900 mb-2">
                      {standard.split(' - ')[0]}
                    </h3>
                    <p className="text-sm text-gray-600">
                      {standard.split(' - ')[1]}
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Products Section */}
      <div className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Produk {config.title}
            </h2>
            <p className="text-lg text-gray-600">
              Pilihan lengkap untuk kebutuhan {industry === 'medical' ? 'medis' : industry === 'industrial' ? 'industri' : industry === 'restaurant' ? 'kuliner' : 'konstruksi'} Anda
            </p>
          </div>
          
          {products.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {products.map((product) => (
                <GloopiProductCard
                  key={product.id}
                  product={product}
                  isB2BCustomer={isB2BCustomer}
                  showIndustryBadge={false}
                />
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <div className="bg-blue-50 rounded-lg p-8 max-w-md mx-auto">
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  Segera Hadir!
                </h3>
                <p className="text-gray-600 mb-4">
                  Produk {config.title.toLowerCase()} akan tersedia dalam Fase {config.phase} pengembangan kami.
                </p>
                <button className="bg-blue-600 text-white px-6 py-2 rounded-lg font-semibold hover:bg-blue-700 transition-colors">
                  Beritahu Saya
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default IndustryLanding
