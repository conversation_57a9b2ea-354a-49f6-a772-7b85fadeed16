"use client"

import { useState } from "react"
import { Button, Input, Textarea, Select } from "@medusajs/ui"
import { CheckCircleSolid, DocumentText } from "@medusajs/icons"

interface QuoteFormData {
    company_name: string
    contact_person: string
    email: string
    phone: string
    product_type: string
    quantity: string
    usage_purpose: string
    delivery_location: string
    additional_notes: string
}

const QuoteRequestForm = () => {
    const [formData, setFormData] = useState<QuoteFormData>({
        company_name: "",
        contact_person: "",
        email: "",
        phone: "",
        product_type: "",
        quantity: "",
        usage_purpose: "",
        delivery_location: "",
        additional_notes: ""
    })

    const [isSubmitting, setIsSubmitting] = useState(false)
    const [isSubmitted, setIsSubmitted] = useState(false)

    const handleInputChange = (field: keyof QuoteFormData, value: string) => {
        setFormData(prev => ({
            ...prev,
            [field]: value
        }))
    }

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault()
        setIsSubmitting(true)

        try {
            // TODO: Send to API endpoint
            await new Promise(resolve => setTimeout(resolve, 2000)) // Simulate API call
            setIsSubmitted(true)
        } catch (error) {
            console.error("Error submitting quote request:", error)
        } finally {
            setIsSubmitting(false)
        }
    }

    if (isSubmitted) {
        return (
            <div className="max-w-2xl mx-auto text-center p-8">
                <div className="mx-auto h-16 w-16 rounded-full bg-green-100 flex items-center justify-center mb-6">
                    <CheckCircleSolid className="h-8 w-8 text-green-600" />
                </div>
                <h3 className="text-2xl font-bold text-gray-900 mb-4">
                    Permintaan Penawaran Terkirim!
                </h3>
                <p className="text-lg text-gray-600 mb-6">
                    Terima kasih atas kepercayaan Anda. Tim sales kami akan menghubungi Anda dalam waktu maksimal 2 jam kerja.
                </p>
                <div className="bg-blue-50 rounded-lg p-4 text-left">
                    <h4 className="font-semibold text-blue-900 mb-2">Langkah Selanjutnya:</h4>
                    <ul className="space-y-1 text-blue-800">
                        <li>• Tim sales akan menghubungi via WhatsApp/Email</li>
                        <li>• Anda akan menerima penawaran harga detail</li>
                        <li>• Diskusi spesifikasi dan negosiasi volume</li>
                        <li>• Proses pemesanan dan pembayaran DP</li>
                    </ul>
                </div>
            </div>
        )
    }

    return (
        <div className="max-w-2xl mx-auto">
            <div className="text-center mb-8">
                <DocumentText className="mx-auto h-12 w-12 text-blue-600 mb-4" />
                <h2 className="text-3xl font-bold text-gray-900">
                    Request for Quotation (RFQ)
                </h2>
                <p className="mt-4 text-lg text-gray-600">
                    Dapatkan penawaran harga terbaik dalam waktu kurang dari 2 jam
                </p>
            </div>

            <form onSubmit={handleSubmit} className="space-y-6">
                {/* Company Information */}
                <div className="bg-gray-50 rounded-lg p-6">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">Informasi Perusahaan</h3>
                    <div className="grid gap-4 md:grid-cols-2">
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                Nama Perusahaan *
                            </label>
                            <Input
                                required
                                value={formData.company_name}
                                onChange={(e) => handleInputChange("company_name", e.target.value)}
                                placeholder="PT. Contoh Perusahaan"
                            />
                        </div>
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                Nama Kontak Person *
                            </label>
                            <Input
                                required
                                value={formData.contact_person}
                                onChange={(e) => handleInputChange("contact_person", e.target.value)}
                                placeholder="Budi Santoso"
                            />
                        </div>
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                Email *
                            </label>
                            <Input
                                type="email"
                                required
                                value={formData.email}
                                onChange={(e) => handleInputChange("email", e.target.value)}
                                placeholder="<EMAIL>"
                            />
                        </div>
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                No. WhatsApp *
                            </label>
                            <Input
                                required
                                value={formData.phone}
                                onChange={(e) => handleInputChange("phone", e.target.value)}
                                placeholder="08123456789"
                            />
                        </div>
                    </div>
                </div>

                {/* Product Requirements */}
                <div className="bg-gray-50 rounded-lg p-6">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">Kebutuhan Produk</h3>
                    <div className="space-y-4">
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                Jenis Sarung Tangan *
                            </label>
                            <Select
                                value={formData.product_type}
                                onValueChange={(value) => handleInputChange("product_type", value)}
                            >
                                <Select.Trigger>
                                    <Select.Value placeholder="Pilih jenis sarung tangan" />
                                </Select.Trigger>
                                <Select.Content>
                                    <Select.Item value="latex-powdered">Latex Powdered</Select.Item>
                                    <Select.Item value="latex-powder-free">Latex Powder Free</Select.Item>
                                    <Select.Item value="nitrile-blue">Nitrile Blue</Select.Item>
                                    <Select.Item value="nitrile-black">Nitrile Black</Select.Item>
                                    <Select.Item value="vinyl-clear">Vinyl Clear</Select.Item>
                                    <Select.Item value="pe-transparent">PE Transparent</Select.Item>
                                    <Select.Item value="other">Lainnya (tulis di catatan)</Select.Item>
                                </Select.Content>
                            </Select>
                        </div>

                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                Quantity yang Dibutuhkan *
                            </label>
                            <Input
                                required
                                value={formData.quantity}
                                onChange={(e) => handleInputChange("quantity", e.target.value)}
                                placeholder="Contoh: 100 box, 10 carton, 1000 pieces"
                            />
                        </div>

                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                Tujuan Penggunaan *
                            </label>
                            <Select
                                value={formData.usage_purpose}
                                onValueChange={(value) => handleInputChange("usage_purpose", value)}
                            >
                                <Select.Trigger>
                                    <Select.Value placeholder="Pilih tujuan penggunaan" />
                                </Select.Trigger>
                                <Select.Content>
                                    <Select.Item value="medical">Medis (Rumah Sakit/Klinik)</Select.Item>
                                    <Select.Item value="food-service">Food Service (Restoran/Catering)</Select.Item>
                                    <Select.Item value="industrial">Industri (Manufaktur/Pabrik)</Select.Item>
                                    <Select.Item value="reseller">Reseller/Distributor</Select.Item>
                                    <Select.Item value="other">Lainnya</Select.Item>
                                </Select.Content>
                            </Select>
                        </div>

                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                Lokasi Pengiriman *
                            </label>
                            <Input
                                required
                                value={formData.delivery_location}
                                onChange={(e) => handleInputChange("delivery_location", e.target.value)}
                                placeholder="Kota/Kabupaten, Provinsi"
                            />
                        </div>

                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                Catatan Tambahan
                            </label>
                            <Textarea
                                value={formData.additional_notes}
                                onChange={(e) => handleInputChange("additional_notes", e.target.value)}
                                placeholder="Spesifikasi khusus, warna, ketebalan, atau informasi lainnya..."
                                rows={4}
                            />
                        </div>
                    </div>
                </div>

                {/* Submit Button */}
                <Button
                    type="submit"
                    size="large"
                    className="w-full"
                    disabled={isSubmitting}
                >
                    {isSubmitting ? "Mengirim..." : "Kirim Permintaan Penawaran"}
                </Button>

                {/* Trust Message */}
                <div className="text-center">
                    <p className="text-sm text-gray-500">
                        🔒 Data Anda aman dan hanya digunakan untuk proses penawaran
                    </p>
                </div>
            </form>
        </div>
    )
}

export default QuoteRequestForm
