"use client"

import { Badge } from "@medusajs/ui"
import { Star, ShoppingCart } from "@medusajs/icons"
import Image from "next/image"
import Link from "next/link"
import { GloveProduct } from "@/types/gloves"

interface GloopiProductCardProps {
  product: GloveProduct
  isB2BCustomer?: boolean
  showIndustryBadge?: boolean
}

const industryLabels = {
  medical: "Medis",
  industrial: "Industri",
  restaurant: "Restoran",
  construction: "Konstruksi"
}

const industryColors = {
  medical: "bg-blue-100 text-blue-800",
  industrial: "bg-orange-100 text-orange-800",
  restaurant: "bg-green-100 text-green-800",
  construction: "bg-yellow-100 text-yellow-800"
}

const GloopiProductCard = ({ 
  product, 
  isB2BCustomer = false, 
  showIndustryBadge = true 
}: GloopiProductCardProps) => {
  const currentPrice = isB2BCustomer 
    ? product.pricing.b2b.perBox 
    : product.pricing.b2c.perBox

  const savings = isB2BCustomer 
    ? ((product.pricing.b2c.perBox - product.pricing.b2b.perBox) / product.pricing.b2c.perBox * 100).toFixed(0)
    : null

  return (
    <div className="group bg-white rounded-xl shadow-md hover:shadow-xl transition-all duration-300 overflow-hidden border border-gray-200">
      <Link href={`/products/${product.id}`}>
        <div className="relative aspect-[4/3] overflow-hidden">
          <Image
            src={product.images[0] || "https://images.unsplash.com/photo-1584308666744-24d5c474f2ae?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80"}
            alt={product.name}
            fill
            className="object-cover group-hover:scale-105 transition-transform duration-300"
          />
          
          {/* Industry Badge */}
          {showIndustryBadge && (
            <div className="absolute top-3 left-3">
              <Badge className={`text-xs font-semibold ${industryColors[product.industry]}`}>
                {industryLabels[product.industry]}
              </Badge>
            </div>
          )}

          {/* B2B Savings Badge */}
          {isB2BCustomer && savings && parseInt(savings) > 0 && (
            <div className="absolute top-3 right-3">
              <Badge className="bg-green-600 text-white text-xs font-semibold">
                Hemat {savings}%
              </Badge>
            </div>
          )}

          {/* Compliance Standards */}
          <div className="absolute bottom-3 left-3 flex flex-wrap gap-1">
            {product.industryAttributes.complianceStandards.slice(0, 2).map((standard) => (
              <Badge 
                key={standard.id}
                className="bg-white/90 text-gray-700 text-xs backdrop-blur-sm"
              >
                {standard.name}
              </Badge>
            ))}
          </div>
        </div>
      </Link>

      <div className="p-4">
        <div className="mb-2">
          <Badge className="bg-gray-100 text-gray-600 text-xs">
            {product.brand}
          </Badge>
        </div>

        <Link href={`/products/${product.id}`}>
          <h3 className="font-semibold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors line-clamp-2">
            {product.name}
          </h3>
        </Link>

        <p className="text-sm text-gray-600 mb-3 line-clamp-2">
          {product.description}
        </p>

        {/* Product Features */}
        <div className="mb-3">
          <div className="flex flex-wrap gap-1">
            {product.industry === 'medical' && 'specialFeatures' in product.industryAttributes && 
             product.industryAttributes.specialFeatures.slice(0, 2).map((feature: string, index: number) => (
              <Badge key={index} className="bg-blue-50 text-blue-700 text-xs">
                {feature}
              </Badge>
            ))}
            {product.industry === 'medical' && 'specialFeatures' in product.industryAttributes && 
             product.industryAttributes.specialFeatures.length > 2 && (
              <Badge className="bg-gray-50 text-gray-600 text-xs">
                +{product.industryAttributes.specialFeatures.length - 2} lainnya
              </Badge>
            )}
          </div>
        </div>

        {/* Reviews */}
        <div className="flex items-center mb-3">
          <div className="flex items-center">
            {[...Array(5)].map((_, i) => (
              <Star
                key={i}
                className={`w-4 h-4 ${
                  i < Math.floor(product.reviews.averageRating)
                    ? "text-yellow-400 fill-current"
                    : "text-gray-300"
                }`}
              />
            ))}
          </div>
          <span className="ml-2 text-sm text-gray-600">
            {product.reviews.averageRating} ({product.reviews.totalReviews})
          </span>
        </div>

        {/* Pricing */}
        <div className="mb-4">
          <div className="flex items-center justify-between">
            <div>
              <div className="text-lg font-bold text-gray-900">
                Rp {currentPrice.toLocaleString('id-ID')}
              </div>
              <div className="text-xs text-gray-500">
                per box ({product.packaging.unitsPerBox} pcs)
              </div>
            </div>
            {isB2BCustomer && (
              <div className="text-right">
                <div className="text-sm text-gray-500 line-through">
                  Rp {product.pricing.b2c.perBox.toLocaleString('id-ID')}
                </div>
                <div className="text-xs text-green-600 font-semibold">
                  Harga B2B
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Sizes */}
        <div className="mb-4">
          <div className="text-xs text-gray-600 mb-1">Ukuran tersedia:</div>
          <div className="flex flex-wrap gap-1">
            {product.sizes.slice(0, 4).map((size) => (
              <Badge key={size} className="bg-gray-100 text-gray-700 text-xs">
                {size}
              </Badge>
            ))}
            {product.sizes.length > 4 && (
              <Badge className="bg-gray-100 text-gray-700 text-xs">
                +{product.sizes.length - 4}
              </Badge>
            )}
          </div>
        </div>

        {/* Action Button */}
        <button className="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg font-semibold transition-colors flex items-center justify-center">
          <ShoppingCart className="w-4 h-4 mr-2" />
          {isB2BCustomer ? "Tambah ke Keranjang B2B" : "Tambah ke Keranjang"}
        </button>
      </div>
    </div>
  )
}

export default GloopiProductCard
