"use client"

import { Badge } from "@medusajs/ui"
import { 
  MedicalGloveAttributes, 
  IndustrialGloveAttributes, 
  RestaurantGloveAttributes, 
  ConstructionGloveAttributes,
  IndustrySpecificAttributes 
} from "@/types/gloves"

interface IndustryAttributesProps {
  industry: "medical" | "industrial" | "restaurant" | "construction"
  attributes: IndustrySpecificAttributes
}

const MedicalAttributes = ({ attributes }: { attributes: MedicalGloveAttributes }) => (
  <div className="space-y-4">
    <div className="grid grid-cols-2 gap-4">
      <div>
        <label className="text-sm font-medium text-gray-600">Tingkat</label>
        <Badge className={`ml-2 ${attributes.grade === "surgical" ? "bg-blue-100 text-blue-800" : "bg-gray-100 text-gray-800"}`}>
          {attributes.grade === "surgical" ? "Bedah" : "Pemeriksaan"}
        </Badge>
      </div>
      <div>
        <label className="text-sm font-medium text-gray-600">Sterilitas</label>
        <Badge className={`ml-2 ${attributes.sterility === "sterile" ? "bg-green-100 text-green-800" : "bg-gray-100 text-gray-800"}`}>
          {attributes.sterility === "sterile" ? "Steril" : "Non-Steril"}
        </Badge>
      </div>
    </div>
    
    <div className="grid grid-cols-2 gap-4">
      <div>
        <label className="text-sm font-medium text-gray-600">Kandungan Bedak</label>
        <Badge className={`ml-2 ${attributes.powderContent === "powder-free" ? "bg-green-100 text-green-800" : "bg-yellow-100 text-yellow-800"}`}>
          {attributes.powderContent === "powder-free" ? "Bebas Bedak" : "Bertalc"}
        </Badge>
      </div>
      <div>
        <label className="text-sm font-medium text-gray-600">Ketebalan</label>
        <span className="ml-2 text-sm font-semibold">
          {attributes.thickness.value} {attributes.thickness.unit}
        </span>
      </div>
    </div>

    {attributes.specialFeatures.length > 0 && (
      <div>
        <label className="text-sm font-medium text-gray-600 block mb-2">Fitur Khusus</label>
        <div className="flex flex-wrap gap-2">
          {attributes.specialFeatures.map((feature, index) => (
            <Badge key={index} className="bg-blue-50 text-blue-700 border-blue-200">
              {feature}
            </Badge>
          ))}
        </div>
      </div>
    )}
  </div>
)

const IndustrialAttributes = ({ attributes }: { attributes: IndustrialGloveAttributes }) => (
  <div className="space-y-4">
    <div className="grid grid-cols-2 gap-4">
      <div>
        <label className="text-sm font-medium text-gray-600">Tingkat Tahan Potong</label>
        <Badge className="ml-2 bg-orange-100 text-orange-800">
          {attributes.cutResistanceLevel}
        </Badge>
      </div>
      <div>
        <label className="text-sm font-medium text-gray-600">Jenis Cengkeraman</label>
        <span className="ml-2 text-sm font-semibold capitalize">
          {attributes.gripType}
        </span>
      </div>
    </div>

    <div>
      <label className="text-sm font-medium text-gray-600 block mb-2">Perlindungan Termal</label>
      <div className="bg-gray-50 p-3 rounded-lg">
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <span className="text-gray-600">Tahan Panas:</span>
            <span className="ml-2 font-semibold">{attributes.thermalProtection.heatResistance}°C</span>
          </div>
          <div>
            <span className="text-gray-600">Tahan Dingin:</span>
            <span className="ml-2 font-semibold">{attributes.thermalProtection.coldResistance}°C</span>
          </div>
        </div>
      </div>
    </div>

    {attributes.chemicalResistance.length > 0 && (
      <div>
        <label className="text-sm font-medium text-gray-600 block mb-2">Tahan Kimia</label>
        <div className="flex flex-wrap gap-2">
          {attributes.chemicalResistance.map((chemical, index) => (
            <Badge key={index} className="bg-orange-50 text-orange-700 border-orange-200">
              {chemical}
            </Badge>
          ))}
        </div>
      </div>
    )}
  </div>
)

const RestaurantAttributes = ({ attributes }: { attributes: RestaurantGloveAttributes }) => (
  <div className="space-y-4">
    <div className="grid grid-cols-2 gap-4">
      <div>
        <label className="text-sm font-medium text-gray-600">Kontak Makanan</label>
        <Badge className={`ml-2 ${attributes.foodContact ? "bg-green-100 text-green-800" : "bg-gray-100 text-gray-800"}`}>
          {attributes.foodContact ? "Food Grade" : "Non-Food Grade"}
        </Badge>
      </div>
      <div>
        <label className="text-sm font-medium text-gray-600">Rentang Suhu</label>
        <span className="ml-2 text-sm font-semibold">
          {attributes.temperatureRange.min}°C - {attributes.temperatureRange.max}°C
        </span>
      </div>
    </div>

    {attributes.allergenFree.length > 0 && (
      <div>
        <label className="text-sm font-medium text-gray-600 block mb-2">Bebas Alergen</label>
        <div className="flex flex-wrap gap-2">
          {attributes.allergenFree.map((allergen, index) => (
            <Badge key={index} className="bg-green-50 text-green-700 border-green-200">
              Bebas {allergen}
            </Badge>
          ))}
        </div>
      </div>
    )}
  </div>
)

const ConstructionAttributes = ({ attributes }: { attributes: ConstructionGloveAttributes }) => (
  <div className="space-y-4">
    <div className="grid grid-cols-2 gap-4">
      <div>
        <label className="text-sm font-medium text-gray-600">Perlindungan Benturan</label>
        <Badge className={`ml-2 ${attributes.impactProtection ? "bg-green-100 text-green-800" : "bg-gray-100 text-gray-800"}`}>
          {attributes.impactProtection ? "Ya" : "Tidak"}
        </Badge>
      </div>
      <div>
        <label className="text-sm font-medium text-gray-600">Tahan Cuaca</label>
        <Badge className="ml-2 capitalize bg-blue-50 text-blue-700 border-blue-200">
          {attributes.weatherResistance}
        </Badge>
      </div>
    </div>

    <div className="grid grid-cols-2 gap-4">
      <div>
        <label className="text-sm font-medium text-gray-600">Rating Daya Tahan</label>
        <div className="ml-2 flex items-center">
          {[...Array(5)].map((_, i) => (
            <span
              key={i}
              className={`text-lg ${
                i < attributes.durabilityRating ? "text-yellow-400" : "text-gray-300"
              }`}
            >
              ★
            </span>
          ))}
          <span className="ml-2 text-sm text-gray-600">
            ({attributes.durabilityRating}/5)
          </span>
        </div>
      </div>
    </div>

    {attributes.reinforcement.length > 0 && (
      <div>
        <label className="text-sm font-medium text-gray-600 block mb-2">Penguatan</label>
        <div className="flex flex-wrap gap-2">
          {attributes.reinforcement.map((reinforcement, index) => (
            <Badge key={index} className="bg-yellow-50 text-yellow-700 border-yellow-200">
              {reinforcement}
            </Badge>
          ))}
        </div>
      </div>
    )}
  </div>
)

const IndustryAttributes = ({ industry, attributes }: IndustryAttributesProps) => {
  const renderAttributes = () => {
    switch (industry) {
      case "medical":
        return <MedicalAttributes attributes={attributes as MedicalGloveAttributes} />
      case "industrial":
        return <IndustrialAttributes attributes={attributes as IndustrialGloveAttributes} />
      case "restaurant":
        return <RestaurantAttributes attributes={attributes as RestaurantGloveAttributes} />
      case "construction":
        return <ConstructionAttributes attributes={attributes as ConstructionGloveAttributes} />
      default:
        return null
    }
  }

  const industryTitles = {
    medical: "Spesifikasi Medis",
    industrial: "Spesifikasi Industri",
    restaurant: "Spesifikasi Kuliner",
    construction: "Spesifikasi Konstruksi"
  }

  return (
    <div className="bg-white rounded-lg border border-gray-200 p-6">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">
        {industryTitles[industry]}
      </h3>
      {renderAttributes()}
    </div>
  )
}

export default IndustryAttributes
