"use client"

import { <PERSON><PERSON>, <PERSON><PERSON> } from "@medusajs/ui"
import { ShoppingCart, Users, CurrencyDollar } from "@medusajs/icons"
import { useState } from "react"

interface PricingTier {
  minQuantity: number
  discountPercentage: number
}

interface PricingData {
  b2c: {
    perBox: number
    perCase: number
  }
  b2b: {
    perBox: number
    perCase: number
    bulkDiscounts: PricingTier[]
  }
}

interface B2BPricingProps {
  pricing: PricingData
  packaging: {
    unitsPerBox: number
    boxesPerCase: number
  }
  isB2BCustomer: boolean
  onRequestQuote?: () => void
}

const B2BPricing = ({ pricing, packaging, isB2BCustomer, onRequestQuote }: B2BPricingProps) => {
  const [selectedQuantity, setSelectedQuantity] = useState(1)
  const [quantityType, setQuantityType] = useState<"box" | "case">("box")

  const calculateB2BPrice = (basePrice: number, quantity: number) => {
    const applicableDiscount = pricing.b2b.bulkDiscounts
      .filter(tier => quantity >= tier.minQuantity)
      .sort((a, b) => b.discountPercentage - a.discountPercentage)[0]

    if (applicableDiscount) {
      return basePrice * (1 - applicableDiscount.discountPercentage / 100)
    }
    return basePrice
  }

  const currentPrice = isB2BCustomer 
    ? calculateB2BPrice(
        quantityType === "box" ? pricing.b2b.perBox : pricing.b2b.perCase,
        selectedQuantity
      )
    : quantityType === "box" ? pricing.b2c.perBox : pricing.b2c.perCase

  const totalPrice = currentPrice * selectedQuantity

  return (
    <div className="bg-white rounded-lg border border-gray-200 p-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-900">Harga & Pemesanan</h3>
        {isB2BCustomer && (
          <Badge className="bg-blue-600 text-white">
            <Users className="w-4 h-4 mr-1" />
            Harga B2B
          </Badge>
        )}
      </div>

      {/* Quantity Selector */}
      <div className="mb-6">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Pilih Kemasan & Jumlah
        </label>
        <div className="flex space-x-4 mb-4">
          <button
            onClick={() => setQuantityType("box")}
            className={`flex-1 p-3 rounded-lg border text-center transition-colors ${
              quantityType === "box"
                ? "border-blue-500 bg-blue-50 text-blue-700"
                : "border-gray-200 hover:border-gray-300"
            }`}
          >
            <div className="font-semibold">Per Box</div>
            <div className="text-sm text-gray-600">{packaging.unitsPerBox} pcs</div>
          </button>
          <button
            onClick={() => setQuantityType("case")}
            className={`flex-1 p-3 rounded-lg border text-center transition-colors ${
              quantityType === "case"
                ? "border-blue-500 bg-blue-50 text-blue-700"
                : "border-gray-200 hover:border-gray-300"
            }`}
          >
            <div className="font-semibold">Per Case</div>
            <div className="text-sm text-gray-600">
              {packaging.boxesPerCase} boxes ({packaging.unitsPerBox * packaging.boxesPerCase} pcs)
            </div>
          </button>
        </div>

        <div className="flex items-center space-x-2">
          <input
            type="number"
            min="1"
            value={selectedQuantity}
            onChange={(e) => setSelectedQuantity(parseInt(e.target.value) || 1)}
            className="w-20 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
          <span className="text-sm text-gray-600">
            {quantityType === "box" ? "boxes" : "cases"}
          </span>
        </div>
      </div>

      {/* Price Display */}
      <div className="bg-gray-50 rounded-lg p-4 mb-6">
        <div className="flex justify-between items-center mb-2">
          <span className="text-sm text-gray-600">Harga per {quantityType}</span>
          <span className="text-lg font-semibold">
            Rp {currentPrice.toLocaleString('id-ID')}
          </span>
        </div>
        <div className="flex justify-between items-center text-lg font-bold">
          <span>Total ({selectedQuantity} {quantityType}s)</span>
          <span className="text-blue-600">
            Rp {totalPrice.toLocaleString('id-ID')}
          </span>
        </div>
        
        {!isB2BCustomer && (
          <div className="mt-2 text-xs text-gray-500">
            *Harga retail. Daftar sebagai pelanggan B2B untuk harga grosir.
          </div>
        )}
      </div>

      {/* B2B Bulk Discounts */}
      {isB2BCustomer && pricing.b2b.bulkDiscounts.length > 0 && (
        <div className="mb-6">
          <h4 className="text-sm font-medium text-gray-700 mb-3">Diskon Volume B2B</h4>
          <div className="space-y-2">
            {pricing.b2b.bulkDiscounts.map((tier, index) => (
              <div
                key={index}
                className={`flex justify-between items-center p-2 rounded text-sm ${
                  selectedQuantity >= tier.minQuantity
                    ? "bg-green-50 text-green-700 border border-green-200"
                    : "bg-gray-50 text-gray-600"
                }`}
              >
                <span>Min. {tier.minQuantity} {quantityType}s</span>
                <Badge className={selectedQuantity >= tier.minQuantity ? "bg-green-600 text-white" : "bg-gray-200 text-gray-700"}>
                  {tier.discountPercentage}% OFF
                </Badge>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Action Buttons */}
      <div className="space-y-3">
        <Button className="w-full bg-blue-600 hover:bg-blue-700">
          <ShoppingCart className="w-4 h-4 mr-2" />
          Tambah ke Keranjang
        </Button>
        
        {isB2BCustomer && onRequestQuote && (
          <Button variant="secondary" className="w-full" onClick={onRequestQuote}>
            <CurrencyDollar className="w-4 h-4 mr-2" />
            Minta Penawaran Khusus
          </Button>
        )}
        
        {!isB2BCustomer && (
          <div className="text-center">
            <Button variant="transparent" className="text-blue-600 hover:text-blue-700">
              Daftar B2B untuk Harga Grosir
            </Button>
          </div>
        )}
      </div>

      {/* Additional Info */}
      <div className="mt-6 pt-4 border-t border-gray-200">
        <div className="text-xs text-gray-500 space-y-1">
          <div>• Gratis ongkir untuk pembelian minimal Rp 500.000</div>
          <div>• Garansi kualitas 100%</div>
          {isB2BCustomer && (
            <div>• Terms pembayaran: Net 30 hari untuk pelanggan B2B</div>
          )}
        </div>
      </div>
    </div>
  )
}

export default B2BPricing
