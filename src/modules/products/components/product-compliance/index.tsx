"use client"

import { Badge } from "@medusajs/ui"
import { ComplianceStandard } from "@/types/gloves"
import { CheckCircleSolid, InformationCircleSolid } from "@medusajs/icons"

interface ProductComplianceProps {
  standards: ComplianceStandard[]
  industry: "medical" | "industrial" | "restaurant" | "construction"
}

const industryColors = {
  medical: "bg-blue-50 text-blue-700 border-blue-200",
  industrial: "bg-orange-50 text-orange-700 border-orange-200",
  restaurant: "bg-green-50 text-green-700 border-green-200",
  construction: "bg-yellow-50 text-yellow-700 border-yellow-200"
}

const ProductCompliance = ({ standards, industry }: ProductComplianceProps) => {
  return (
    <div className="bg-white rounded-lg border border-gray-200 p-6">
      <div className="flex items-center mb-4">
        <CheckCircleSolid className="w-5 h-5 text-green-600 mr-2" />
        <h3 className="text-lg font-semibold text-gray-900">
          Standar Ke<PERSON>han & Sertifikasi
        </h3>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
        {standards.map((standard) => (
          <div
            key={standard.id}
            className={`p-4 rounded-lg border ${industryColors[industry]}`}
          >
            <div className="flex items-start justify-between mb-2">
              <Badge variant="secondary" className="font-semibold">
                {standard.name}
              </Badge>
              <span className="text-xs text-gray-500 uppercase">
                {standard.region}
              </span>
            </div>
            <p className="text-sm text-gray-700 mb-2">
              {standard.description}
            </p>
            <p className="text-xs text-gray-600">
              Disertifikasi oleh: {standard.certificationBody}
            </p>
          </div>
        ))}
      </div>

      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="flex items-start">
          <InformationCircleSolid className="w-5 h-5 text-blue-600 mr-2 mt-0.5 flex-shrink-0" />
          <div>
            <h4 className="font-semibold text-blue-900 mb-1">
              Mengapa Standar Kepatuhan Penting?
            </h4>
            <p className="text-sm text-blue-800">
              Standar internasional memastikan produk kami memenuhi persyaratan kualitas, 
              keamanan, dan performa yang ketat untuk industri {industry === 'medical' ? 'medis' : 
              industry === 'industrial' ? 'industri' : 
              industry === 'restaurant' ? 'kuliner' : 'konstruksi'}.
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}

export default ProductCompliance
