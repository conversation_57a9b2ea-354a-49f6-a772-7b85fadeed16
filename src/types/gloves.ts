// Industry-specific glove types and attributes for Gloopi.id

export interface ComplianceStandard {
  id: string
  name: string
  description: string
  certificationBody: string
  region: "EU" | "US" | "GLOBAL"
}

export interface GloveAttribute {
  id: string
  name: string
  value: string
  unit?: string
  displayOrder: number
}

export interface MedicalGloveAttributes {
  grade: "examination" | "surgical"
  sterility: "sterile" | "non-sterile"
  powderContent: "powdered" | "powder-free"
  thickness: {
    value: number
    unit: "mm" | "mil"
  }
  specialFeatures: string[]
  complianceStandards: ComplianceStandard[]
}

export interface IndustrialGloveAttributes {
  cutResistanceLevel: "A1" | "A2" | "A3" | "A4" | "A5" | "B" | "C" | "D" | "E" | "F"
  chemicalResistance: string[]
  thermalProtection: {
    heatResistance: number
    coldResistance: number
    unit: "°C"
  }
  gripType: "smooth" | "textured" | "dotted" | "palm-coated"
  complianceStandards: ComplianceStandard[]
}

export interface RestaurantGloveAttributes {
  foodContact: boolean
  allergenFree: string[]
  temperatureRange: {
    min: number
    max: number
    unit: "°C"
  }
  complianceStandards: ComplianceStandard[]
}

export interface ConstructionGloveAttributes {
  impactProtection: boolean
  weatherResistance: "waterproof" | "water-resistant" | "none"
  reinforcement: string[]
  durabilityRating: 1 | 2 | 3 | 4 | 5
  complianceStandards: ComplianceStandard[]
}

export type IndustrySpecificAttributes = 
  | MedicalGloveAttributes
  | IndustrialGloveAttributes
  | RestaurantGloveAttributes
  | ConstructionGloveAttributes

export interface GloveProduct {
  id: string
  name: string
  description: string
  industry: "medical" | "industrial" | "restaurant" | "construction"
  material: "nitrile" | "latex" | "vinyl" | "leather" | "cotton" | "synthetic"
  sizes: string[]
  colors: string[]
  packaging: {
    unitsPerBox: number
    boxesPerCase: number
  }
  pricing: {
    b2c: {
      perBox: number
      perCase: number
    }
    b2b: {
      perBox: number
      perCase: number
      bulkDiscounts: {
        minQuantity: number
        discountPercentage: number
      }[]
    }
  }
  brand: string
  sku: string
  images: string[]
  reviews: {
    averageRating: number
    totalReviews: number
  }
  industryAttributes: IndustrySpecificAttributes
  generalAttributes: GloveAttribute[]
}

// Compliance Standards Database
export const COMPLIANCE_STANDARDS: Record<string, ComplianceStandard> = {
  "EN_455": {
    id: "EN_455",
    name: "EN 455",
    description: "European standard for medical gloves",
    certificationBody: "European Committee for Standardization",
    region: "EU"
  },
  "FDA_510K": {
    id: "FDA_510K",
    name: "FDA 510(k)",
    description: "FDA clearance for medical devices",
    certificationBody: "U.S. Food and Drug Administration",
    region: "US"
  },
  "EN_388": {
    id: "EN_388",
    name: "EN 388",
    description: "European standard for protective gloves against mechanical risks",
    certificationBody: "European Committee for Standardization",
    region: "EU"
  },
  "EN_374": {
    id: "EN_374",
    name: "EN 374",
    description: "European standard for protective gloves against chemicals and micro-organisms",
    certificationBody: "European Committee for Standardization",
    region: "EU"
  },
  "EN_1186": {
    id: "EN_1186",
    name: "EN 1186",
    description: "European standard for materials in contact with foodstuffs",
    certificationBody: "European Committee for Standardization",
    region: "EU"
  },
  "FDA_21_CFR_177": {
    id: "FDA_21_CFR_177",
    name: "FDA 21 CFR 177",
    description: "FDA regulation for indirect food additives",
    certificationBody: "U.S. Food and Drug Administration",
    region: "US"
  },
  "ANSI_ISEA": {
    id: "ANSI_ISEA",
    name: "ANSI/ISEA",
    description: "American National Standard for hand protection",
    certificationBody: "American National Standards Institute",
    region: "US"
  }
}

// Industry-specific attribute templates
export const MEDICAL_GLOVE_TEMPLATE: Partial<MedicalGloveAttributes> = {
  complianceStandards: [
    COMPLIANCE_STANDARDS.EN_455,
    COMPLIANCE_STANDARDS.FDA_510K
  ]
}

export const INDUSTRIAL_GLOVE_TEMPLATE: Partial<IndustrialGloveAttributes> = {
  complianceStandards: [
    COMPLIANCE_STANDARDS.EN_388,
    COMPLIANCE_STANDARDS.EN_374
  ]
}

export const RESTAURANT_GLOVE_TEMPLATE: Partial<RestaurantGloveAttributes> = {
  complianceStandards: [
    COMPLIANCE_STANDARDS.EN_1186,
    COMPLIANCE_STANDARDS.FDA_21_CFR_177
  ]
}

export const CONSTRUCTION_GLOVE_TEMPLATE: Partial<ConstructionGloveAttributes> = {
  complianceStandards: [
    COMPLIANCE_STANDARDS.EN_388,
    COMPLIANCE_STANDARDS.ANSI_ISEA
  ]
}
