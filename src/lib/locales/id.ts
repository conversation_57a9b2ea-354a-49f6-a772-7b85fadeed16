export const indonesianTranslations = {
    // Common
    loading: "Memuat...",
    error: "<PERSON><PERSON><PERSON><PERSON> kes<PERSON>",
    success: "<PERSON>rhasil",
    cancel: "<PERSON><PERSON>",
    save: "<PERSON>mp<PERSON>",
    edit: "Edit",
    delete: "<PERSON><PERSON>",
    upload: "Unggah",
    download: "Unduh",

    // Navigation
    home: "Beranda",
    products: "Produk",
    cart: "Keranjang",
    orders: "Pesanan",
    quotes: "Penawaran",
    invoices: "Faktur",
    account: "Akun",

    // Product Related
    addToCart: "Tambah ke Keranjang",
    requestQuote: "Ajukan Penawaran",
    viewDetails: "Lihat Detail",
    specifications: "Spesifikasi",
    pricePerBox: "Harga per Box",
    pricePerCarton: "Harga per Karton",
    quantityDiscount: "Diskon Kuantitas",

    // RFQ & Quotes
    requestForQuotation: "Permintaan Penawaran (RFQ)",
    submitRFQ: "Kirim RFQ",
    quoteStatus: "Status Penawaran",
    pending: "<PERSON>unggu",
    approved: "Disetujui",
    rejected: "Di<PERSON>lak",
    acceptQuote: "Terima Penawaran",
    rejectQuote: "Tolak Penawaran",

    // Invoice & Payment
    invoice: "Faktur",
    invoiceNumber: "Nomor Faktur",
    totalAmount: "Total Jumlah",
    paidAmount: "Jumlah Dibayar",
    dueDate: "Tanggal Jatuh Tempo",
    paymentStatus: "Status Pembayaran",
    uploadPaymentProof: "Unggah Bukti Pembayaran",
    paymentMethod: "Metode Pembayaran",
    bankTransfer: "Transfer Bank",

    // Tax Invoice
    taxInvoice: "Faktur Pajak",
    requestTaxInvoice: "Minta Faktur Pajak",
    npwp: "NPWP",
    ppn: "PPN",
    dpp: "DPP (Dasar Pengenaan Pajak)",

    // Shipping
    shipping: "Pengiriman",
    trackingNumber: "Nomor Resi",
    shippingStatus: "Status Pengiriman",
    preparing: "Sedang Disiapkan",
    packed: "Dikemas",
    shipped: "Dikirim",
    delivered: "Diterima",
    estimatedDelivery: "Estimasi Pengiriman",
    deliveryNote: "Surat Jalan",

    // Company Info
    companyName: "Nama Perusahaan",
    contactPerson: "Narahubung",
    businessType: "Jenis Bisnis",
    clinic: "Klinik",
    restaurant: "Restoran",
    distributor: "Distributor",
    reseller: "Reseller",
    manufacturer: "Manufaktur",

    // Forms
    required: "Wajib diisi",
    email: "Email",
    phone: "Nomor Telepon",
    address: "Alamat",
    city: "Kota",
    postalCode: "Kode Pos",
    notes: "Catatan",
    specialRequirements: "Kebutuhan Khusus",

    // Glove Types & Categories
    gloves: "Sarung Tangan",
    medicalGloves: "Sarung Tangan Medis",
    industrialGloves: "Sarung Tangan Industri",
    foodServiceGloves: "Sarung Tangan F&B",
    disposableGloves: "Sarung Tangan Sekali Pakai",
    latexGloves: "Sarung Tangan Latex",
    nitrileGloves: "Sarung Tangan Nitrile",
    vinylGloves: "Sarung Tangan Vinyl",

    // Business Messages
    heroTitle: "Sarung Tangan Grosir – Bersertifikat & Respon Cepat",
    heroSubtitle: "Solusi terpercaya untuk kebutuhan sarung tangan dalam jumlah besar",
    whyChooseUs: "Mengapa Memilih Kami",
    fastResponse: "Respon Cepat",
    qualityProducts: "Produk Berkualitas",
    competitivePrice: "Harga Kompetitif",
    trustedSupplier: "Supplier Terpercaya",

    // Customer Service
    customerService: "Layanan Pelanggan",
    contactUs: "Hubungi Kami",
    whatsappSupport: "Dukungan WhatsApp",
    helpCenter: "Pusat Bantuan",
    faq: "Pertanyaan Umum",
};
