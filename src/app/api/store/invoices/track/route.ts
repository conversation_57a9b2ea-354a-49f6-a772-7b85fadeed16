import { NextRequest, NextResponse } from "next/server"

export async function GET(request: NextRequest) {
    try {
        const { searchParams } = new URL(request.url)
        const invoiceNumber = searchParams.get("number")

        if (!invoiceNumber) {
            return NextResponse.json(
                { error: "Invoice number is required" },
                { status: 400 }
            )
        }

        // Mock response for now - replace with actual API call
        const mockInvoice = {
            id: "inv_01",
            invoice_number: invoiceNumber,
            company_name: "PT. Contoh Perusahaan",
            total_amount: 10000000,
            dp_amount: 5000000,
            remaining_amount: 5000000,
            status: "partial",
            due_date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
            order_id: "order_001",
            payment_proofs: [
                {
                    id: "proof_01",
                    amount: 5000000,
                    created_at: new Date().toISOString(),
                    status: "verified"
                }
            ]
        }

        // Simulate not found for demo
        if (invoiceNumber === "INV-404") {
            return NextResponse.json(
                { error: "Invoice tidak ditemukan" },
                { status: 404 }
            )
        }

        return NextResponse.json({
            invoice: mockInvoice
        })

    } catch (error) {
        console.error("Error in invoice tracking:", error)
        return NextResponse.json(
            { error: "Internal server error" },
            { status: 500 }
        )
    }
}
