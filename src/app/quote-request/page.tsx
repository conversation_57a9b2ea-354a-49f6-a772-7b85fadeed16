import QuoteRequestForm from "@/modules/quote/components/quote-request-form"

export default function QuoteRequestPage() {
    return (
        <div className="min-h-screen bg-gray-50">
            {/* Header */}
            <div className="bg-white shadow-sm">
                <div className="mx-auto max-w-7xl px-4 py-8 sm:px-6 lg:px-8">
                    <div className="text-center">
                        <h1 className="text-2xl font-bold text-gray-900">
                            Ajukan Permintaan Penawaran
                        </h1>
                        <p className="mt-2 text-gray-600">
                            Dapatkan harga terbaik untuk kebutuhan sarung tangan grosir Anda
                        </p>
                    </div>
                </div>
            </div>

            {/* Content */}
            <div className="mx-auto max-w-7xl px-4 py-12 sm:px-6 lg:px-8">
                <QuoteRequestForm />
            </div>

            {/* Info Section */}
            <div className="bg-white border-t">
                <div className="mx-auto max-w-7xl px-4 py-12 sm:px-6 lg:px-8">
                    <div className="grid gap-8 lg:grid-cols-3">
                        <div className="text-center">
                            <div className="mx-auto h-12 w-12 rounded-full bg-blue-100 flex items-center justify-center mb-4">
                                <span className="text-2xl">⚡</span>
                            </div>
                            <h3 className="text-lg font-semibold text-gray-900">Respon Cepat</h3>
                            <p className="mt-2 text-gray-600">
                                Tim sales kami akan merespon dalam waktu kurang dari 2 jam kerja
                            </p>
                        </div>

                        <div className="text-center">
                            <div className="mx-auto h-12 w-12 rounded-full bg-green-100 flex items-center justify-center mb-4">
                                <span className="text-2xl">💰</span>
                            </div>
                            <h3 className="text-lg font-semibold text-gray-900">Harga Transparan</h3>
                            <p className="mt-2 text-gray-600">
                                Tidak ada mark-up tersembunyi. Dapatkan harga factory langsung
                            </p>
                        </div>

                        <div className="text-center">
                            <div className="mx-auto h-12 w-12 rounded-full bg-purple-100 flex items-center justify-center mb-4">
                                <span className="text-2xl">🤝</span>
                            </div>
                            <h3 className="text-lg font-semibold text-gray-900">Konsultasi Gratis</h3>
                            <p className="mt-2 text-gray-600">
                                Tim ahli kami siap membantu memilih produk yang tepat
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    )
}
