import { listRegions } from "@/lib/data/regions"
import FeaturedProducts from "@/modules/home/<USER>/featured-products"
import GloopiHero from "@/modules/home/<USER>/gloopi-hero"
import GloopiFeatures from "@/modules/home/<USER>/gloopi-features"
import GloopiCategories from "@/modules/home/<USER>/gloopi-categories"
import Testimonials from "@/modules/home/<USER>/testimonials"
import SkeletonFeaturedProducts from "@/modules/skeletons/templates/skeleton-featured-products"
import { Metadata } from "next"
import { Suspense } from "react"

export const dynamicParams = true

export const metadata: Metadata = {
  title: "Gloopi - Sarung Tangan Grosir Bersertifikat | Respon Cepat < 2 Jam",
  description:
    "Supplier sarung tangan grosir terpercaya di Indonesia. Melayani medis, F&B, industri. Latex, Nitrile, Vinyl. Harga factory, respon < 2 jam, bersertifikat ISO & CE.",
}

export async function generateStaticParams() {
  const countryCodes = await listRegions().then(
    (regions) =>
      regions
        ?.map((r) => r.countries?.map((c) => c.iso_2))
        .flat()
        .filter(Boolean) as string[]
  )
  return countryCodes.map((countryCode) => ({ countryCode }))
}

export default async function Home(props: {
  params: Promise<{ countryCode: string }>
}) {
  const params = await props.params

  const { countryCode } = params

  return (
    <div className="flex flex-col">
      <GloopiHero />
      <GloopiFeatures />
      <GloopiCategories />
      <div className="bg-gray-50 py-16">
        <div className="max-w-7xl mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Produk Terlaris
            </h2>
            <p className="text-lg text-gray-600">
              Pilihan terpopuler dari ribuan customer kami
            </p>
          </div>
          <Suspense fallback={<SkeletonFeaturedProducts />}>
            <FeaturedProducts countryCode={countryCode} />
          </Suspense>
        </div>
      </div>
      <Testimonials />
    </div>
  )
}
