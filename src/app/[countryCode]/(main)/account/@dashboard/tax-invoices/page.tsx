import { Metadata } from "next"
import { notFound } from "next/navigation"

import { retrieveCustomer } from "@/lib/data/customer"
import { TaxInvoiceManager } from "@/modules/tax-invoice"

export const metadata: Metadata = {
    title: "Faktur Pajak",
    description: "Kelola permintaan dan unduh faktur pajak untuk pesanan Anda.",
}

export default async function TaxInvoicesPage() {
    const customer = await retrieveCustomer()

    if (!customer) {
        notFound()
    }

    return (
        <div className="w-full">
            <div className="mb-8 flex flex-col gap-y-4">
                <h1 className="text-2xl-semi">Faktur Pajak</h1>
                <p className="text-base-regular text-ui-fg-base">
                    Kelola permintaan dan unduh faktur pajak untuk pesanan Anda.
                    Faktur pajak diperlukan untuk keperluan perpajakan perusahaan di Indonesia.
                </p>
            </div>
            <TaxInvoiceManager customerId={customer.id} />
        </div>
    )
}
