import { getBaseURL } from "@/lib/util/env"
import { Toaster } from "@medusajs/ui"
import { Analytics } from "@vercel/analytics/next"
import { GeistSans } from "geist/font/sans"
import { Metadata } from "next"
import "@/styles/globals.css"

export const metadata: Metadata = {
  metadataBase: new URL(getBaseURL()),
  title: {
    default: "Gloopi.id - Sarung Tangan Berkualitas untuk Semua Industri",
    template: "%s | Gloopi.id"
  },
  description: "Platform e-commerce B2B dan B2C untuk sarung tangan berkualitas tinggi. Melayani industri medis, pabrik, restoran, dan konstruksi dengan standar internasional.",
  keywords: ["sarung tangan", "gloves", "medical gloves", "industrial gloves", "restaurant gloves", "B2B gloves", "sarung tangan medis", "sarung tangan industri"],
}

export default function RootLayout(props: { children: React.ReactNode }) {
  return (
    <html lang="en" data-mode="light" className={GeistSans.variable}>
      <body>
        <main className="relative">{props.children}</main>
        <Toaster className="z-[99999]" position="bottom-left" />
        <Analytics />
      </body>
    </html>
  )
}
