### [cite_start]I<PERSON><PERSON>k [cite: 2]

[cite_start]Gloopi.id adalah platform _e-commerce_ yang bertujuan untuk menyediakan sarung tangan berkualitas tinggi untuk berbagai industri, termasuk medis, pabrik, restoran, dan konstruk<PERSON>[cite: 3]. [cite_start]Proyek ini berfokus pada fase awal peluncuran dengan menjual sarung tangan medis[cite: 4]. [cite_start]Platform ini akan melayani dua segmen pelanggan yang berbeda, yaitu _Business-to-Business_ (B2B) dan _Business-to-Consumer_ (B2C)[cite: 14]. [cite_start]Laporan ini menguraikan persyaratan tata letak situs web, struktur navigasi, strategi konten, nada konten, dan atribut produk yang diperlukan untuk memastikan peluncuran yang sukses dan skalabilitas platform[cite: 5].

### II. [cite_start]Tujuan Produk [cite: 18, 20]

Tujuan produk ini adalah untuk:

- [cite_start]Menguraikan fungsionalitas penting, prinsi<PERSON> desain, strategi konten, dan atribut produk yang diperlukan untuk pengembangan platform _e-commerce_ Gloopi.id[cite: 20].
- [cite_start]Memberikan cetak biru fundamental bagi tim pengembangan untuk memastikan platform yang dibangun kuat, mudah digunakan, sangat skalabel, dan sesuai dengan standar industri yang relevan[cite: 21].
- [cite_start]Mengidentifikasi persyaratan produk di awal untuk memitigasi risiko, mengoptimalkan proses pengembangan, dan meletakkan dasar untuk pertumbuhan berkelanjutan di pasar sarung tangan multi-industri[cite: 22].

### III. [cite_start]Persona Pengguna [cite: 25]

[cite_start]Gloopi.id akan melayani dua segmen pengguna yang berbeda, masing-masing dengan motivasi, perilaku, dan kebutuhan interaksi yang unik[cite: 25].

#### [cite_start]1. Persona B2B: "Profesional Pengadaan" [cite: 26]

- [cite_start]**Deskripsi:** Mewakili pengambil keputusan di organisasi, seperti manajer pengadaan, administrator klinik, atau pemimpin rantai pasokan rumah sakit[cite: 27].
- [cite_start]**Tujuan Utama:** Mendapatkan sarung tangan berkualitas tinggi yang sesuai dengan peraturan, dengan harga massal yang kompetitif, dan memastikan pasokan yang konsisten[cite: 28].
- **Kebutuhan:**
  - [cite_start]Akses ke spesifikasi teknis dan sertifikasi produk yang terperinci[cite: 30].
  - [cite_start]Proses pemesanan yang efisien[cite: 29].
  - [cite_start]Kemampuan untuk melacak pesanan dan mengelola akun[cite: 29].
  - [cite_start]Dasbor yang dipersonalisasi yang menampilkan riwayat pesanan, detail akun, dan harga yang disesuaikan[cite: 34].
- **_Pain Points_:**
  - [cite_start]Kurangnya informasi produk yang terperinci[cite: 31].
  - [cite_start]Kesulitan membandingkan spesifikasi yang kompleks[cite: 31].
  - [cite_start]Harga yang tidak transparan untuk pesanan massal[cite: 31].

#### [cite_start]2. Persona B2C: "Profesional Individu/Pengguna Rumahan" [cite: 36]

- [cite_start]**Deskripsi:** Mencakup individu seperti dokter, perawat, ahli kebersihan gigi, atau pengguna rumahan yang memiliki kebutuhan sarung tangan spesifik[cite: 37].
- [cite_start]**Tujuan Utama:** Menemukan sarung tangan yang tepat dengan mudah, memahami manfaat produk secara jelas, dan memiliki pengalaman pembelian yang cepat dan langsung[cite: 38].
- **Kebutuhan:**
  - [cite_start]Deskripsi produk yang terperinci namun mudah dipahami[cite: 42].
  - [cite_start]Desain yang bersih dan intuitif[cite: 42].
  - [cite_start]Opsi _guest checkout_[cite: 43].
  - [cite_start]Berbagai pilihan pembayaran[cite: 43].
- **_Pain Points_:**
  - [cite_start]Kebingungan akibat jargon teknis[cite: 40].
  - [cite_start]Proses _checkout_ yang rumit[cite: 40].
  - [cite_start]Kekhawatiran tentang keaslian atau keamanan produk[cite: 40].

### IV. [cite_start]Persyaratan Fungsional [cite: 20]

1.  **Pengalaman Pengguna yang Terdiferensiasi:**

    - [cite_start]Platform harus dapat menyajikan konten, harga, dan fitur yang dinamis berdasarkan jenis pengguna (misalnya, akun B2B yang masuk vs. pembeli B2C tamu)[cite: 57].
    - [cite_start]Untuk B2B, platform harus menyediakan dasbor yang dipersonalisasi, kemampuan pemesanan ulang, harga khusus, dan alur kerja persetujuan pesanan lanjutan[cite: 54, 112].
    - [cite_start]Untuk B2C, platform harus menyederhanakan proses _checkout_, memungkinkan _guest checkout_, dan menerima berbagai opsi pembayaran[cite: 54, 84].

2.  **Manajemen Produk dan Atribut:**

    - [cite_start]Platform harus mendukung model data produk yang fleksibel dan dapat diperluas untuk mengakomodasi berbagai atribut spesifik industri[cite: 245, 248].
    - [cite_start]Atribut produk umum harus mencakup: bahan, ukuran, warna, kemasan, harga, ulasan/peringkat, merek, dan SKU[cite: 169, 177].
    - [cite_start]Atribut spesifik untuk sarung tangan medis harus mencakup: tingkat (pemeriksaan/bedah), sterilitas, kandungan bedak, ketebalan, dan fitur khusus[cite: 180, 185].
    - [cite_start]Platform harus secara eksplisit menampilkan dan menjelaskan standar kepatuhan yang relevan, seperti EN 455 dan peraturan FDA, di halaman produk[cite: 155, 287].

3.  **Navigasi dan Pencarian:**

    - [cite_start]Navigasi harus berlapis, menggabungkan menu tingkat atas horizontal yang sederhana dengan filter bilah sisi yang kuat[cite: 107, 108].
    - [cite_start]Opsi navigasi utama harus mencakup "Belanja Berdasarkan Industri" dan "Belanja Berdasarkan Jenis Sarung Tangan"[cite: 88].
    - [cite_start]Sistem pencarian harus dilengkapi dengan opsi pemfilteran komprehensif yang mencakup atribut umum dan spesifik industri[cite: 95, 96].

4.  **Proses _Checkout_:**
    - [cite_start]Proses _checkout_ harus dirancang untuk efisiensi dan kemudahan penggunaan[cite: 102].
    - [cite_start]Untuk B2C, harus memungkinkan _guest checkout_ dan mendukung berbagai opsi pembayaran[cite: 103].
    - [cite_start]Untuk B2B, harus mendukung opsi pengiriman yang serbaguna, proses pemesanan persetujuan lanjutan, dan kemampuan untuk mendapatkan penawaran harga yang tepat untuk pesanan khusus[cite: 104].

### [cite_start]V. Peta Jalan Implementasi Bertahap [cite: 250, 261]

- [cite_start]**Fase 1 (Peluncuran Awal): Sarung Tangan Medis [cite: 263]**

  - [cite_start]**Fokus:** Menyempurnakan pengalaman pengguna, data produk, dan kepatuhan untuk segmen sarung tangan medis[cite: 263].
  - **Tugas:**
    - [cite_start]Menerapkan sistem Manajemen Informasi Produk (PIM) yang fleksibel[cite: 252].
    - [cite_start]Membangun segmentasi B2B/B2C dengan jenis akun pengguna yang berbeda[cite: 254].
    - [cite_start]Mengisi "Pusat Sumber Daya" dengan penjelasan tentang standar EN 455 dan FDA[cite: 258].

- [cite_start]**Fase 2 (Ekspansi): Sarung Tangan Industri/Pabrik [cite: 265]**

  - [cite_start]**Fokus:** Mengintegrasikan kategori sarung tangan industri/pabrik[cite: 265].
  - **Tugas:**
    - [cite_start]Memperluas PIM untuk menyertakan atribut EN 388 dan EN 374[cite: 266].
    - [cite_start]Menambahkan opsi pemfilteran spesifik industri (misalnya, tingkat potongan, ketahanan kimia)[cite: 267].
    - [cite_start]Membuat halaman arahan dan deskripsi produk yang disesuaikan[cite: 268].

- [cite_start]**Fase 3 (Ekspansi): Sarung Tangan Restoran/Layanan Makanan [cite: 269]**

  - [cite_start]**Fokus:** Mengintegrasikan kategori sarung tangan layanan makanan[cite: 269].
  - **Tugas:**
    - [cite_start]Mengintegrasikan atribut keamanan kontak makanan EN 1186 dan FDA 21 CFR 177[cite: 270].
    - [cite_start]Menekankan kemampuan pemesanan massal dan opsi pesanan berulang untuk kebutuhan pasokan restoran[cite: 271].
    - [cite_start]Membuat konten yang berfokus pada kebersihan dan pencegahan kontaminasi silang[cite: 272].

- [cite_start]**Fase 4 (Ekspansi): Sarung Tangan Konstruksi [cite: 273]**
  - [cite_start]**Fokus:** Mengintegrasikan kategori sarung tangan konstruksi[cite: 273].
  - **Tugas:**
    - [cite_start]Menggabungkan atribut terkait konstruksi seperti jenis cengkeraman, perlindungan cuaca, dan fitur yang diperkuat[cite: 274].
    - [cite_start]Menjelajahi program permintaan sampel untuk perusahaan konstruksi besar[cite: 275].
    - [cite_start]Membuat konten yang menyoroti daya tahan dan perlindungan bahaya spesifik[cite: 276].
