{"name": "medusa-next", "version": "1.0.3", "private": true, "author": "<PERSON><PERSON><PERSON> <<EMAIL>> & <PERSON> <<EMAIL>> (https://www.medusajs.com)", "description": "Medusa B2B Starter Storefront", "keywords": ["medusa-storefront", "medusa-b2b-storefront"], "scripts": {"dev": "next dev -p 8000", "build": "next build", "start": "next start -p 8000", "lint": "next lint", "analyze": "ANALYZE=true next build"}, "resolutions": {"webpack": "^5", "@types/react": "npm:types-react@19.0.0-rc.1", "@types/react-dom": "npm:types-react-dom@19.0.0-rc.1"}, "dependencies": {"@headlessui/react": "^2.2.0", "@hookform/resolvers": "^3.9.0", "@medusajs/icons": "2.8.4", "@medusajs/js-sdk": "2.8.4", "@medusajs/types": "2.8.4", "@medusajs/ui": "4.0.14", "@paypal/paypal-js": "^5.0.6", "@paypal/react-paypal-js": "^7.8.1", "@radix-ui/react-dialog": "^1.1.1", "@stripe/react-stripe-js": "^1.7.2", "@stripe/stripe-js": "^1.29.0", "@vercel/analytics": "^1.4.1", "geist": "^1.3.1", "lodash": "^4.17.21", "next": "^15.3.3", "pg": "^8.11.3", "react": "^19.1.0", "react-country-flag": "^3.0.2", "react-dom": "^19.1.0", "react-hook-form": "^7.53.0", "react-intersection-observer": "^9.3.4", "react-markdown": "^9.0.1", "server-only": "^0.0.1", "webpack": "^5", "zod": "3.22.4"}, "devDependencies": {"@babel/core": "^7.17.5", "@medusajs/ui-preset": "2.8.4", "@types/lodash": "^4.14.195", "@types/node": "17.0.21", "@types/pg": "^8.11.0", "@types/react": "npm:types-react@19.0.0-rc.1", "@types/react-dom": "npm:types-react-dom@19.0.0-rc.1", "@types/wicg-file-system-access": "^2023.10.5", "ansi-colors": "^4.1.3", "autoprefixer": "^10.4.2", "babel-loader": "^8.2.3", "eslint": "8.10.0", "eslint-config-next": "15.0.1", "postcss": "^8.4.8", "prettier": "^2.8.8", "tailwindcss": "^3.4.1", "typescript": "^5.5.3"}, "packageManager": "yarn@4.4.0+sha512.91d93b445d9284e7ed52931369bc89a663414e5582d00eea45c67ddc459a2582919eece27c412d6ffd1bd0793ff35399381cb229326b961798ce4f4cc60ddfdb"}