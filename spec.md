# Gloopi.id - Spesifikasi Teknis Platform E-commerce B2B/B2C

## 1. Ringkasan Proyek

**Gloopi.id** adalah platform e-commerce yang dirancang khusus untuk menyediakan sarung tangan berkualitas tinggi untuk berbagai industri. Platform ini melayani dua segmen pelanggan yang berbeda: Business-to-Business (B2B) dan Business-to-Consumer (B2C), dengan fokus awal pada sarung tangan medis.

### 1.1 Tujuan Utama
- Menyediakan platform e-commerce yang skalabel untuk penjualan sarung tangan multi-industri
- Melayani kebutuhan B2B dengan fitur khusus seperti approval workflow, bulk ordering, dan pricing khusus
- Memberikan pengalaman B2C yang sederhana dan intuitif untuk pembelian individual
- Memastikan kepatuhan terhadap standar industri (EN 455, FDA, EN 388, EN 374, dll.)

### 1.2 Target Industri
1. **Medis** (Fase 1) - Sarung tangan pemeriksaan dan bedah
2. **Industri/Pabrik** (Fase 2) - Sarung tangan tahan potongan dan kimia
3. **Restoran/Layanan Makanan** (Fase 3) - Sarung tangan food-safe
4. **Konstruksi** (Fase 4) - Sarung tangan kerja dan safety

## 2. Arsitektur Teknis

### 2.1 Technology Stack
- **Frontend**: Next.js 15.3.3 dengan React 19.1.0
- **Backend**: Medusa.js (Headless Commerce Platform)
- **Database**: PostgreSQL
- **Styling**: Tailwind CSS 3.4.1
- **UI Components**: @medusajs/ui, @radix-ui, @headlessui
- **Payment**: Stripe, PayPal
- **Analytics**: Vercel Analytics
- **Deployment**: Vercel (Frontend), Custom (Backend)

### 2.2 Struktur Direktori
```
src/
├── app/                    # Next.js App Router
│   ├── [countryCode]/     # Multi-region routing
│   ├── api/               # API routes
│   ├── about/             # Static pages
│   ├── contact/           # Contact page
│   ├── invoice-tracking/  # Invoice tracking
│   └── quote-request/     # Quote request form
├── lib/                   # Utilities dan konfigurasi
│   ├── config.ts         # Medusa SDK configuration
│   ├── constants.tsx     # App constants
│   ├── context/          # React contexts
│   ├── data/             # Data fetching functions
│   ├── hooks/            # Custom hooks
│   ├── locales/          # Internationalization
│   └── util/             # Utility functions
├── modules/               # Feature modules
│   ├── account/          # User account management
│   ├── cart/             # Shopping cart
│   ├── checkout/         # Checkout process
│   ├── home/             # Homepage components
│   ├── products/         # Product catalog
│   ├── quote/            # Quote system
│   ├── invoice/          # Invoice management
│   └── layout/           # Layout components
├── types/                # TypeScript definitions
└── styles/               # Global styles
```

## 3. Fitur Utama Platform

### 3.1 Sistem Autentikasi dan Manajemen User
- **Registrasi B2B**: Otomatis membuat company dan employee record
- **Registrasi B2C**: Registrasi customer standar
- **Login/Logout**: Menggunakan Medusa Auth dengan email/password
- **Profile Management**: Update informasi customer dan company
- **Multi-role Support**: Admin, employee, dan customer roles

### 3.2 Manajemen Produk
- **Product Catalog**: Struktur produk yang fleksibel dengan atribut spesifik industri
- **Multi-variant Products**: Ukuran, warna, material, kemasan
- **Industry-specific Attributes**: 
  - Medis: Grade (examination/surgical), sterility, powder-free, thickness
  - Industri: Cut resistance level, chemical resistance
  - Restoran: Food contact safety, disposable features
  - Konstruksi: Grip type, weather protection, reinforced features
- **Compliance Information**: Sertifikasi dan standar (EN 455, FDA, EN 388, dll.)
- **Search & Filtering**: Advanced filtering berdasarkan industri, material, ukuran, dll.

### 3.3 Shopping Cart dan Checkout

#### 3.3.1 B2C Features
- **Guest Checkout**: Pembelian tanpa registrasi
- **Simple Cart**: Add/remove items, quantity adjustment
- **Multiple Payment Methods**: Credit card (Stripe), PayPal
- **Standard Shipping**: Pilihan pengiriman standar

#### 3.3.2 B2B Features
- **Company Cart**: Cart terkait dengan company
- **Bulk Ordering**: Pemesanan dalam jumlah besar
- **Approval Workflow**: 
  - Requires admin approval
  - Requires sales manager approval
  - Spending limit checks
- **Custom Pricing**: Harga khusus berdasarkan company
- **Purchase Orders**: Support untuk PO numbers
- **Flexible Shipping**: Multiple shipping addresses

### 3.4 Sistem Quote (B2B)
- **Quote Request Form**: Form permintaan penawaran dengan detail lengkap
- **Quote Management**: Admin dapat membuat dan mengelola quotes
- **Quote to Order**: Konversi quote menjadi order
- **Message System**: Komunikasi antara customer dan admin
- **Quote History**: Riwayat semua quotes

### 3.5 Invoice dan Document Management
- **Invoice Generation**: Generate invoice otomatis
- **Invoice Tracking**: Track status pembayaran invoice
- **Document Download**: Download invoice dan dokumen lainnya
- **Tax Invoice**: Support untuk tax invoice Indonesia

### 3.6 Account Dashboard

#### 3.6.1 B2C Dashboard
- **Order History**: Riwayat pemesanan
- **Profile Management**: Update informasi personal
- **Address Book**: Manajemen alamat pengiriman

#### 3.6.2 B2B Dashboard
- **Company Overview**: Informasi company dan spending
- **Order Management**: Riwayat dan status orders
- **Approval Management**: Manage pending approvals
- **Employee Management**: Manage company employees
- **Spending Limits**: Monitor dan manage spending limits
- **Custom Pricing**: View negotiated prices

## 4. Diferensiasi B2B vs B2C

### 4.1 User Experience
| Feature | B2C | B2B |
|---------|-----|-----|
| Registration | Simple customer signup | Company + employee creation |
| Pricing | Standard retail prices | Custom negotiated pricing |
| Checkout | Guest checkout available | Requires login |
| Payment | Immediate payment | Invoice/PO options |
| Approval | None | Multi-level approval workflow |
| Ordering | Individual items | Bulk quantities |
| Dashboard | Simple order history | Complex company dashboard |

### 4.2 Technical Implementation
- **User Detection**: Berdasarkan customer.employee relationship
- **Dynamic Pricing**: Conditional pricing berdasarkan company
- **Conditional Features**: Feature flags berdasarkan user type
- **Different Workflows**: Separate checkout flows

## 5. API Integrations

### 5.1 Medusa Backend Integration
- **Store API**: Product catalog, cart, checkout, orders
- **Admin API**: Company management, quotes, approvals
- **Auth API**: Customer authentication
- **Custom Modules**: Quote system, company management, approval workflow

### 5.2 Payment Integrations
- **Stripe**: Credit card processing untuk B2C dan B2B
- **PayPal**: Alternative payment method
- **Manual Payment**: Invoice-based payment untuk B2B

### 5.3 Analytics
- **Vercel Analytics**: User behavior tracking
- **Custom Events**: Login, purchase, quote requests

## 6. Data Models

### 6.1 Core Entities
- **Customer**: Standard Medusa customer dengan extensions
- **Company**: B2B company information
- **Employee**: Relationship antara customer dan company
- **Quote**: Quote requests dan management
- **Approval**: Approval workflow untuk B2B orders
- **Cart**: Extended dengan company dan approval information
- **Order**: Extended dengan company information

### 6.2 Key Relationships
```
Customer 1:1 Employee N:1 Company
Cart N:1 Company
Cart 1:N Approval
Quote N:1 Customer
Quote N:1 Company
Order N:1 Company
```

## 7. Konfigurasi dan Environment

### 7.1 Environment Variables
```
NEXT_PUBLIC_MEDUSA_BACKEND_URL=http://localhost:9000
NEXT_PUBLIC_MEDUSA_PUBLISHABLE_KEY=pk_test
NEXT_PUBLIC_BASE_URL=http://localhost:8000
NEXT_PUBLIC_DEFAULT_REGION=us
```

### 7.2 Development Setup
1. Clone repository
2. Install dependencies: `yarn install`
3. Setup environment variables
4. Start development server: `yarn dev`
5. Access at http://localhost:8000

## 8. Deployment dan Production

### 8.1 Frontend Deployment
- **Platform**: Vercel
- **Build Command**: `yarn build`
- **Output**: Static site dengan API routes

### 8.2 Backend Requirements
- **Medusa Server**: Custom Medusa instance dengan modules
- **Database**: PostgreSQL
- **File Storage**: S3 atau compatible storage
- **Environment**: Node.js production environment

## 9. Security dan Compliance

### 9.1 Authentication Security
- JWT-based authentication
- Secure cookie handling
- CORS configuration
- Rate limiting

### 9.2 Data Protection
- Customer data encryption
- PCI compliance untuk payment
- GDPR compliance considerations
- Secure API endpoints

## 10. Performance Optimizations

### 10.1 Frontend Optimizations
- Next.js App Router untuk optimal routing
- Image optimization dengan Next.js Image
- Code splitting dan lazy loading
- Caching strategies

### 10.2 Backend Optimizations
- Database indexing
- API response caching
- CDN untuk static assets
- Optimized queries

## 11. Monitoring dan Analytics

### 11.1 Application Monitoring
- Error tracking dan logging
- Performance monitoring
- Uptime monitoring
- User session tracking

### 11.2 Business Analytics
- Sales tracking
- Customer behavior analysis
- Conversion funnel analysis
- B2B vs B2C performance comparison

## 12. Detail Implementasi Modules

### 12.1 Account Module (`src/modules/account/`)
- **Login Component**: Email/password authentication dengan error handling
- **Register Component**: B2B/B2C registration dengan company creation
- **Overview Component**: Dashboard dengan order history dan account info
- **Profile Management**: Update customer information
- **Address Management**: Shipping dan billing addresses

### 12.2 Cart Module (`src/modules/cart/`)
- **Cart Context**: Global state management untuk cart
- **Cart Drawer**: Slide-out cart dengan item management
- **Approval Status Banner**: Menampilkan status approval untuk B2B
- **Spending Limit Check**: Validasi spending limit untuk employees
- **Empty Cart Handling**: Fallback untuk cart kosong

### 12.3 Checkout Module (`src/modules/checkout/`)
- **Payment Wrapper**: Stripe dan PayPal integration
- **Checkout Form**: Multi-step checkout dengan validation
- **Address Components**: Shipping dan billing address forms
- **Payment Methods**: Dynamic payment method selection
- **Company Information**: B2B company details dalam checkout
- **Approval Workflow**: Integration dengan approval system

### 12.4 Products Module (`src/modules/products/`)
- **Product Templates**: Detail product pages dengan specifications
- **Product Cards**: Reusable product display components
- **Gloopi Product Card**: Custom product card dengan industry badges
- **Search dan Filtering**: Advanced product search capabilities
- **Product Variants**: Size, color, material selection

### 12.5 Quote Module (`src/modules/quote/`)
- **Quote Request Form**: Comprehensive form untuk B2B quote requests
- **Quote Management**: Admin interface untuk manage quotes
- **Quote History**: Customer view untuk quote history
- **Message System**: Communication antara customer dan admin
- **Quote to Order**: Conversion dari quote ke actual order

### 12.6 Invoice Module (`src/modules/invoice/`)
- **Invoice Generation**: Automatic invoice creation
- **Invoice Tracking**: Status tracking dan payment monitoring
- **Document Download**: PDF generation dan download
- **Tax Invoice**: Indonesian tax invoice compliance

### 12.7 Home Module (`src/modules/home/<USER>
- **Gloopi Hero**: Homepage hero section dengan CTA
- **Gloopi Features**: Feature highlights dan benefits
- **Gloopi Categories**: Industry category navigation
- **Featured Products**: Dynamic product recommendations
- **Testimonials**: Customer testimonials dan reviews

## 13. Data Layer dan API Integration

### 13.1 Data Fetching Patterns (`src/lib/data/`)
- **Server Actions**: Next.js server actions untuk mutations
- **Cache Management**: Revalidation strategies dengan tags
- **Error Handling**: Consistent error handling across APIs
- **Authentication Headers**: Automatic auth header injection

### 13.2 Key Data Functions
```typescript
// Customer Management
retrieveCustomer(): Promise<B2BCustomer>
login(formData: FormData): Promise<void>
signup(formData: FormData): Promise<Customer>
updateCustomer(data: UpdateCustomer): Promise<Customer>

// Cart Management
retrieveCart(cartId?: string): Promise<B2BCart>
addToCart(variantId: string, quantity: number): Promise<void>
updateCart(data: UpdateCart): Promise<B2BCart>
createCartApproval(cartId: string): Promise<Approval>

// Order Management
listOrders(): Promise<B2BOrder[]>
retrieveOrder(orderId: string): Promise<B2BOrder>
placeOrder(cartId: string): Promise<B2BOrder>

// Company Management
createCompany(data: CreateCompany): Promise<Company>
createEmployee(data: CreateEmployee): Promise<Employee>
```

### 13.3 Type Definitions (`src/types/`)
- **Global Types**: B2BCart, B2BOrder, B2BCustomer
- **Company Types**: QueryCompany, QueryEmployee
- **Quote Types**: ModuleQuote, QueryQuote, QuoteMessage
- **Approval Types**: QueryApproval, ApprovalStatus
- **Gloves Types**: GloveProduct dengan industry-specific attributes

## 14. UI Components dan Design System

### 14.1 Design System
- **Medusa UI**: Base component library
- **Radix UI**: Headless UI primitives
- **Tailwind CSS**: Utility-first styling
- **Custom Components**: Gloopi-specific components

### 14.2 Key UI Patterns
- **Responsive Design**: Mobile-first approach
- **Loading States**: Skeleton components dan spinners
- **Error States**: Consistent error messaging
- **Form Validation**: Real-time validation dengan error display
- **Modal Dialogs**: Confirmation dan detail modals

### 14.3 Accessibility
- **ARIA Labels**: Proper accessibility labels
- **Keyboard Navigation**: Full keyboard support
- **Screen Reader**: Compatible dengan screen readers
- **Color Contrast**: WCAG compliant color schemes

## 15. Internationalization dan Localization

### 15.1 Multi-Region Support
- **Country Code Routing**: `/[countryCode]/` dynamic routing
- **Region Detection**: Automatic region detection via middleware
- **Currency Support**: Multi-currency pricing
- **Shipping Zones**: Region-specific shipping options

### 15.2 Language Support
- **Indonesian Primary**: Bahasa Indonesia sebagai bahasa utama
- **English Support**: English language support
- **Localized Content**: Region-specific content dan pricing

## 16. Testing dan Quality Assurance

### 16.1 Testing Strategy
- **Unit Testing**: Component dan utility function testing
- **Integration Testing**: API integration testing
- **E2E Testing**: Critical user journey testing
- **Performance Testing**: Load testing untuk B2B scenarios

### 16.2 Code Quality
- **TypeScript**: Full TypeScript implementation
- **ESLint**: Code linting dengan Next.js config
- **Prettier**: Code formatting
- **Git Hooks**: Pre-commit quality checks

## 17. Future Roadmap

### 17.1 Fase Pengembangan
1. **Fase 1** (Current): Sarung tangan medis
2. **Fase 2**: Ekspansi ke sarung tangan industri
3. **Fase 3**: Sarung tangan restoran/food service
4. **Fase 4**: Sarung tangan konstruksi

### 17.2 Planned Features
- Mobile app development
- Advanced analytics dashboard
- Inventory management integration
- Multi-language support
- Advanced B2B features (contracts, recurring orders)
- Integration dengan ERP systems
- Real-time chat support
- Advanced reporting dan analytics
- Subscription-based ordering
- Multi-warehouse support
